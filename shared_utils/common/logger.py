import logging
import os
import sys
from datetime import datetime

class Logger:
    """
    Logger utility for the ZTB automation framework
    """
    
    @staticmethod
    def initialize_logger(log_file_name, log_level="INFO", log_dir=None):
        """
        Initialize logger with file and console handlers
        
        Args:
            log_file_name (str): Name of the log file
            log_level (str): Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_dir (str): Directory to store log files
            
        Returns:
            logging.Logger: Configured logger instance
        """
        if log_dir is None:
            log_dir = os.path.join(os.getcwd(), "Reports", "logs")
        
        # Create log directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)
        
        # Create logger
        logger = logging.getLogger(log_file_name)
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        console_formatter = logging.Formatter(
            '%(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        # File handler
        log_file_path = os.path.join(log_dir, f"{log_file_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        file_handler = logging.FileHandler(log_file_path)
        file_handler.setLevel(getattr(logging, log_level.upper()))
        file_handler.setFormatter(file_formatter)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(console_formatter)
        
        # Add handlers to logger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        logger.info(f"Logger initialized: {log_file_name}")
        logger.info(f"Log file: {log_file_path}")
        
        return logger