# -*- coding: utf-8 -*-
import os
import sys
import inspect
import subprocess
import platform
import re
import datetime

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)

# Detect operating system
os_version = None
if platform.uname()[0] == 'Darwin':
    os_version = "Mac"
    HOME_DIR = os.path.expanduser('~')
elif platform.uname()[0] == 'Linux':
    os_version = 'Linux'
    HOME_DIR = os.path.expanduser('~')
elif platform.uname()[0] == 'Windows':
    os_version = "Windows"
else:
    os_version = "Unknown"
    print("Warning: OS version cannot be determined")

class Utils:
    """
    Shared utility constants for the ZTB automation framework.
    """
    # Path configurations
    if os_version == "Mac":
        # User never logs in as root, sometimes depending on what process launched automation, the user is set to root
        current_user = os.getlogin()
        if current_user == 'root':
            try:
                print("Fetching current logged in user")
                command = "who"
                process = os.popen(cmd=command)
                process_output = process.read()
                current_user = process_output.split(" ")[0]
            except Exception as e:
                print(f"Error while fetching current logged in user: {e}")
            else:
                print(f"Current logged in user: {current_user}")
        
        LOG_PATH = '/Library/Application Support/ZTBAutomation/'
        TEMP_PATH = '/tmp/ztb_automation/'
        APPLICATIONS_PATH = '/Applications/'
        DOWNLOADS_PATH = f'/Users/<USER>/Downloads/'
        DESKTOP_PATH = f'/Users/<USER>/Desktop/'
        DOCUMENTS_PATH = f'/Users/<USER>/Documents/'
        
    elif os_version == "Linux":
        LOG_PATH = "/var/log/ztb_automation/"
        TEMP_PATH = "/tmp/ztb_automation/"
        APPLICATIONS_PATH = "/opt/"
        DOWNLOADS_PATH = os.path.join(HOME_DIR, 'Downloads')
        DESKTOP_PATH = os.path.join(HOME_DIR, 'Desktop')
        DOCUMENTS_PATH = os.path.join(HOME_DIR, 'Documents')
        
    else:  # Windows or Unknown
        LOG_PATH = 'C:\\ProgramData\\ZTBAutomation\\'
        TEMP_PATH = 'C:\\temp\\ztb_automation\\'
        APPLICATIONS_PATH = 'C:\\Program Files\\'
        DOWNLOADS_PATH = f"C:\\Users\\<USER>\\Downloads\\"
        DESKTOP_PATH = f"C:\\Users\\<USER>\\Desktop\\"
        DOCUMENTS_PATH = f"C:\\Users\\<USER>\\Documents\\"
    
    # ZTB automation specific paths
    ZTB_ARCHIVE_PATH = os.path.join(os.getcwd(), "OS_Mac", "resource")
    INSTALLER_MOUNT_POINT = "/Volumes/"
    
    # Sample ZTB files for testing (these would be placed in resource folder)
    SAMPLE_ZTB_FILES = {
        "test_app": "TestApp-1.0.0.dmg",
        "sample_utility": "SampleUtility-2.1.0.dmg",
        "demo_software": "DemoSoftware-3.0.dmg"
    }
    
    # Installation timeouts (in seconds)
    MOUNT_TIMEOUT = 30
    APP_COPY_TIMEOUT = 120
    UNMOUNT_TIMEOUT = 30
    VERIFICATION_TIMEOUT = 60
    
    # Common application bundle extensions
    APP_EXTENSIONS = ['.app', '.pkg', '.mpkg']
    
    # Installer verification patterns
    INSTALLER_VERIFICATION_PATTERNS = {
        'mounted': r'\/Volumes\/.*',
        'application': r'.*\.app$',
        'package': r'.*\.pkg$'
    }
    
    # Error messages
    ERROR_MESSAGES = {
        'installer_not_found': "Installer file not found at specified path",
        'mount_failed': "Failed to mount installer image",
        'copy_failed': "Failed to copy application to Applications folder",
        'unmount_failed': "Failed to unmount installer image",
        'verification_failed': "Application verification failed",
        'cleanup_failed': "Cleanup operation failed"
    }
    
    # Success messages
    SUCCESS_MESSAGES = {
        'installer_mounted': "Installer image mounted successfully",
        'app_copied': "Application copied successfully",
        'installer_unmounted': "Installer image unmounted successfully",
        'verification_passed': "Application verification passed",
        'cleanup_completed': "Cleanup completed successfully"
    }

class ZTBInstallationConstants(Utils):
    """
    Constants specific to ZTB installation operations
    """
    # Installation modes
    INSTALLATION_MODES = {
        'copy_app': 'copy_application',
        'run_installer': 'run_installer_package',
        'custom_script': 'custom_installation_script'
    }
    
    # Verification methods
    VERIFICATION_METHODS = {
        'file_exists': 'check_file_existence',
        'app_bundle': 'verify_app_bundle',
        'launch_test': 'test_application_launch',
        'version_check': 'verify_application_version'
    }
    
    # Cleanup strategies
    CLEANUP_STRATEGIES = {
        'remove_app': 'remove_from_applications',
        'uninstaller': 'run_uninstaller',
        'manual_cleanup': 'manual_file_removal'
    }

class TestConfiguration(Utils):
    """
    Test configuration constants
    """
    # Test data paths
    TEST_DATA_PATH = os.path.join(os.getcwd(), "OS_Mac", "resource", "test_data")
    TEST_REPORTS_PATH = os.path.join(os.getcwd(), "Reports")
    
    # Test timeouts
    TEST_TIMEOUT_SHORT = 30
    TEST_TIMEOUT_MEDIUM = 120
    TEST_TIMEOUT_LONG = 300
    
    # Test markers
    TEST_MARKERS = {
        'smoke': 'Quick smoke tests',
        'regression': 'Full regression suite',
        'installation': 'Agent installation tests',
        'verification': 'Application verification tests',
        'cleanup': 'Cleanup and uninstallation tests'
    }