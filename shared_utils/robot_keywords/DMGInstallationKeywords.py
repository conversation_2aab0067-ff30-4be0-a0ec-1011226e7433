#!/usr/bin/env python3
################################################################
#               ROBOT FRAMEWORK KEYWORDS FOR DMG INSTALLATION #
#                                                              #
# AUTHOR : ZTB Automation Framework                           #
################################################################

"""
Robot Framework keywords for DMG installation automation.
"""

import os
import sys
import time
import subprocess
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from OS_Mac.library.agent_installation import ZTBInstaller
from shared_utils.common.logger import Logger

try:
    import pyautogui
    import pygetwindow as gw
    PYAUTOGUI_AVAILABLE = True
    # Configure PyAutoGUI
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 1
except ImportError:
    PYAUTOGUI_AVAILABLE = False


class DMGInstallationKeywords:
    """
    Robot Framework keywords for DMG installation automation
    """
    
    ROBOT_LIBRARY_SCOPE = 'GLOBAL'
    
    def __init__(self):
        self.logger = Logger.initialize_logger("robot_dmg_keywords.log", log_level="DEBUG")
        self.installer = None
        self.mounted_volumes = []
        
    def mount_dmg_file(self, dmg_filename):
        """
        Mount a DMG file and return the result.
        
        Args:
            dmg_filename: Name of the DMG file in the resource directory
            
        Returns:
            Tuple: (success, message, mount_path)
        """
        try:
            if not self.installer:
                self.installer = ZTBInstaller(verify_dmg_exists=False, log_handle=self.logger)
            
            dmg_path = os.path.join(self.installer.const.ZTB_ARCHIVE_PATH, dmg_filename)
            
            if not os.path.exists(dmg_path):
                return (False, f"DMG file not found: {dmg_path}", None)
            
            result = self.installer.mount_dmg(dmg_path)
            
            if result[0]:
                self.mounted_volumes.append(result[2])
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error mounting DMG: {e}")
            return (False, f"Error mounting DMG: {e}", None)
    
    def unmount_dmg(self, mount_path):
        """
        Unmount a DMG at the specified path.
        
        Args:
            mount_path: Path to the mounted volume
            
        Returns:
            Tuple: (success, message, None)
        """
        try:
            if not self.installer:
                self.installer = ZTBInstaller(verify_dmg_exists=False, log_handle=self.logger)
            
            result = self.installer.unmount_dmg(mount_path)
            
            if result[0] and mount_path in self.mounted_volumes:
                self.mounted_volumes.remove(mount_path)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error unmounting DMG: {e}")
            return (False, f"Error unmounting DMG: {e}", None)
    
    def list_directory_contents(self, directory_path):
        """
        List contents of a directory.
        
        Args:
            directory_path: Path to the directory
            
        Returns:
            List of directory contents
        """
        try:
            if os.path.exists(directory_path):
                return os.listdir(directory_path)
            else:
                return []
        except Exception as e:
            self.logger.error(f"Error listing directory contents: {e}")
            return []
    
    def find_package_files(self, directory_path):
        """
        Find installer package files in a directory.
        
        Args:
            directory_path: Path to search for packages
            
        Returns:
            List of package files
        """
        try:
            contents = self.list_directory_contents(directory_path)
            pkg_files = [f for f in contents if f.endswith(('.pkg', '.mpkg'))]
            return pkg_files
        except Exception as e:
            self.logger.error(f"Error finding package files: {e}")
            return []
    
    def launch_installer_gui(self, pkg_path):
        """
        Launch the installer GUI for a package.
        
        Args:
            pkg_path: Path to the installer package
            
        Returns:
            Boolean: True if launched successfully
        """
        try:
            if not os.path.exists(pkg_path):
                self.logger.error(f"Package file not found: {pkg_path}")
                return False
            
            # Use 'open' command to launch the installer GUI
            result = subprocess.run(
                ["open", pkg_path],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                self.logger.info(f"Installer GUI launched for: {pkg_path}")
                return True
            else:
                self.logger.error(f"Failed to launch installer GUI: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error launching installer GUI: {e}")
            return False
    
    def find_installer_window(self, window_title_contains="Installer"):
        """
        Find the installer window.
        
        Args:
            window_title_contains: Text that should be in the window title
            
        Returns:
            Boolean: True if window found
        """
        try:
            if not PYAUTOGUI_AVAILABLE:
                self.logger.warning("PyAutoGUI not available - cannot find windows")
                return False
            
            # Get all windows and search for installer
            all_windows = gw.getAllWindows()

            # First try exact match
            for window in all_windows:
                if window_title_contains in window.title:
                    self.logger.info(f"Found installer window: {window.title}")
                    return True

            # Then try partial match
            for window in all_windows:
                if window_title_contains.lower() in window.title.lower():
                    self.logger.info(f"Found installer window by partial match: {window.title}")
                    return True
            
            self.logger.warning(f"No installer window found containing '{window_title_contains}'")
            return False
            
        except Exception as e:
            self.logger.error(f"Error finding installer window: {e}")
            return False
    
    def activate_installer_window(self):
        """
        Activate the installer window.
        """
        try:
            if not PYAUTOGUI_AVAILABLE:
                return False
            
            # Find installer window
            all_windows = gw.getAllWindows()
            windows = []

            # Look for installer windows
            for window in all_windows:
                if "installer" in window.title.lower():
                    windows = [window]
                    break
            
            if windows:
                windows[0].activate()
                time.sleep(1)
                self.logger.info(f"Activated installer window: {windows[0].title}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error activating installer window: {e}")
            return False
    
    def click_continue_button(self):
        """
        Click the Continue button in the installer.
        """
        try:
            if not PYAUTOGUI_AVAILABLE:
                return False
            
            # Try different button texts
            button_texts = ["Continue", "Next", "Agree", "Install"]
            
            for text in button_texts:
                try:
                    # Look for button text on screen
                    button_location = pyautogui.locateOnScreen(text, confidence=0.8)
                    if button_location:
                        center = pyautogui.center(button_location)
                        pyautogui.click(center)
                        self.logger.info(f"Clicked button: {text}")
                        return True
                except pyautogui.ImageNotFoundException:
                    continue
            
            # Try common button locations
            common_locations = [(800, 500), (700, 450), (600, 400)]
            for x, y in common_locations:
                pyautogui.click(x, y)
                time.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error clicking continue button: {e}")
            return False
    
    def accept_license_agreement(self):
        """
        Accept the license agreement.
        """
        try:
            if not PYAUTOGUI_AVAILABLE:
                return False
            
            # Try to find and click Agree button or checkbox
            agree_texts = ["Agree", "I Agree", "Accept"]
            
            for text in agree_texts:
                try:
                    button_location = pyautogui.locateOnScreen(text, confidence=0.8)
                    if button_location:
                        center = pyautogui.center(button_location)
                        pyautogui.click(center)
                        self.logger.info(f"Clicked: {text}")
                        return True
                except pyautogui.ImageNotFoundException:
                    continue
            
            # Try pressing spacebar to check agreement checkbox
            pyautogui.press('space')
            time.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error accepting license agreement: {e}")
            return False
    
    def click_install_button(self):
        """
        Click the Install button.
        """
        try:
            if not PYAUTOGUI_AVAILABLE:
                return False
            
            install_texts = ["Install", "Start Installation", "Begin Installation"]
            
            for text in install_texts:
                try:
                    button_location = pyautogui.locateOnScreen(text, confidence=0.8)
                    if button_location:
                        center = pyautogui.center(button_location)
                        pyautogui.click(center)
                        self.logger.info(f"Clicked: {text}")
                        return True
                except pyautogui.ImageNotFoundException:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error clicking install button: {e}")
            return False
    
    def wait_for_installation_completion(self, timeout=300):
        """
        Wait for installation to complete.
        
        Args:
            timeout: Maximum time to wait in seconds
            
        Returns:
            Boolean: True if installation completed
        """
        try:
            if not PYAUTOGUI_AVAILABLE:
                # Just wait for the timeout period
                time.sleep(min(timeout, 60))
                return True
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                # Look for completion indicators
                completion_texts = ["Installation was successful", "Close", "Done", "Finish"]
                
                for text in completion_texts:
                    try:
                        if pyautogui.locateOnScreen(text, confidence=0.8):
                            self.logger.info(f"Found completion indicator: {text}")
                            return True
                    except pyautogui.ImageNotFoundException:
                        pass
                
                time.sleep(5)  # Check every 5 seconds
            
            self.logger.warning(f"Installation did not complete within {timeout} seconds")
            return False
            
        except Exception as e:
            self.logger.error(f"Error waiting for installation completion: {e}")
            return False
    
    def close_installer_window(self):
        """
        Close the installer window.
        """
        try:
            if not PYAUTOGUI_AVAILABLE:
                return True
            
            # Try to click Close button
            close_texts = ["Close", "Done", "Finish", "Quit"]
            
            for text in close_texts:
                try:
                    button_location = pyautogui.locateOnScreen(text, confidence=0.8)
                    if button_location:
                        center = pyautogui.center(button_location)
                        pyautogui.click(center)
                        self.logger.info(f"Clicked: {text}")
                        return True
                except pyautogui.ImageNotFoundException:
                    continue
            
            # Try pressing Cmd+Q to quit
            pyautogui.hotkey('cmd', 'q')
            time.sleep(2)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error closing installer window: {e}")
            return False
    
    def close_all_installer_windows(self):
        """
        Close all installer windows.
        """
        try:
            if not PYAUTOGUI_AVAILABLE:
                return True
            
            # Find all installer windows and close them
            all_windows = gw.getAllWindows()
            for window in all_windows:
                if "installer" in window.title.lower():
                    try:
                        window.close()
                        self.logger.info(f"Closed window: {window.title}")
                    except:
                        pass
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error closing installer windows: {e}")
            return False
    
    def run_quiet_installation(self, pkg_path):
        """
        Run quiet installation using command line.
        
        Args:
            pkg_path: Path to the installer package
            
        Returns:
            Tuple: (success, message)
        """
        try:
            install_command = [
                "sudo", "installer", 
                "-pkg", pkg_path, 
                "-target", "/",
                "-verbose"
            ]
            
            self.logger.info(f"Running quiet installation: {' '.join(install_command)}")
            
            result = subprocess.run(
                install_command,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                return (True, "Installation completed successfully")
            else:
                error_msg = result.stderr if result.stderr else f"Return code: {result.returncode}"
                return (False, f"Installation failed: {error_msg}")
                
        except subprocess.TimeoutExpired:
            return (False, "Installation timed out")
        except Exception as e:
            return (False, f"Error during installation: {e}")
    
    def cleanup_all_mounted_volumes(self):
        """
        Cleanup all mounted volumes tracked by this instance.
        """
        try:
            if not self.installer:
                return True
            
            result = self.installer.cleanup_all_mounted_volumes()
            self.mounted_volumes.clear()
            return result[0]
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            return False
