################################################################
#               HELPER FUNCTIONS MODULE                       #
#                                                              #
# AUTHOR : ZTB Automation Framework                           #
################################################################

import os
import json
import time
import hashlib
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from shared_utils.common.logger import Logger


class Helpers:
    """
    Collection of OS-agnostic, shared utility helper functions.
    """

    @staticmethod
    def get_timestamp(format_string="%Y%m%d_%H%M%S"):
        """
        Get current timestamp as a formatted string.
        
        Args:
            format_string (str): The format for the timestamp string.
            
        Returns:
            str: Formatted timestamp string.
        """
        return datetime.now().strftime(format_string)

    @staticmethod
    def create_temp_directory(prefix="ztb_auto_"):
        """
        Create a temporary directory.
        
        Args:
            prefix (str): A prefix for the temporary directory name.
            
        Returns:
            str: The path to the created temporary directory, or None on error.
        """
        try:
            temp_dir = tempfile.mkdtemp(prefix=prefix)
            return temp_dir
        except Exception as e:
            # A static helper shouldn't assume a logger exists.
            # Printing is a reasonable fallback for a utility script.
            print(f"Error creating temporary directory: {e}")
            return None

    @staticmethod
    def cleanup_temp_directory(directory_path):
        """
        Remove a temporary directory and its contents.
        
        Args:
            directory_path (str): The path to the directory to remove.
            
        Returns:
            bool: True if cleanup was successful or directory didn't exist, False otherwise.
        """
        try:
            if directory_path and os.path.exists(directory_path):
                shutil.rmtree(directory_path)
                return True
            return True # It's clean if it doesn't exist
        except Exception as e:
            print(f"Error cleaning up temporary directory {directory_path}: {e}")
            return False

    @staticmethod
    def get_file_hash(file_path, algorithm='sha256'):
        """
        Calculate hash of a file
        
        Args:
            file_path (str): Path to the file
            algorithm (str): Hash algorithm (md5, sha1, sha256, sha512)
            
        Returns:
            str: File hash or None if error
        """
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            print(f"Error calculating file hash: {e}")
            return None

    @staticmethod
    def format_file_size(size_bytes):
        """
        Format file size in human readable format
        
        Args:
            size_bytes (int): Size in bytes
            
        Returns:
            str: Formatted size string
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"

    @staticmethod
    def validate_dmg_file(dmg_path):
        """
        Basic validation of a DMG installer file
        
        Args:
            dmg_path (str): Path to DMG file
            
        Returns:
            tuple: (is_valid, error_message)
        """
        try:
            if not os.path.exists(dmg_path):
                return (False, "DMG file does not exist")
            
            if not dmg_path.lower().endswith('.dmg'):
                return (False, "File does not have .dmg extension")
            
            # Check file size (should be > 0)
            if os.path.getsize(dmg_path) == 0:
                return (False, "DMG file is empty")
            
            return (True, "DMG file appears valid")
            
        except Exception as e:
            return (False, f"Error validating DMG file: {e}")