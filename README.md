# ZTB Automation Framework

A comprehensive automation framework for installing and managing ZTB components (e.g., agent, browser extension) on macOS systems. This framework provides robust tools for mounting installer images, copying applications, running installers, and verifying installations.

## Features

- **Automated Installer Mounting**: Mount and unmount installer images (e.g., .dmg) programmatically
- **Application Installation**: Copy .app bundles to Applications directory
- **Package Installation**: Run .pkg and .mpkg installer packages
- **Installation Verification**: Verify successful installations and app bundle integrity
- **Comprehensive Logging**: Detailed logging with configurable levels
- **Test Framework**: Complete pytest-based testing suite
- **Error Handling**: Robust error handling and cleanup mechanisms
- **System Operations**: System information gathering, process management, and ZTB-specific tasks like managing isolation sessions.

## Directory Structure

```
ztb_automation/
├── OS_Mac/                     # macOS-specific automation code
│   ├── library/               # Core automation libraries
│   │   ├── ztb_installation.py   # Main ZTB component installation class
│   │   └── system_ops.py         # System operations utilities
│   ├── module/                # Feature-specific modules
│   ├── resource/              # ZTB installer files and test resources
│   └── test_suite/            # Test cases
│       ├── daily_sanity/      # Smoke tests
│       ├── ztb_installation/  # Installation tests
│       └── ztb_verification/  # Verification tests
├── shared_utils/              # Shared, OS-agnostic utilities
│   ├── constants.py         # Configuration constants
│   ├── logger.py            # Logging utilities
│   └── helpers.py           # Helper functions
├── config/                   # Configuration files
├── Reports/                  # Test reports and logs
├── Extras/                   # Additional resources
├── requirements.txt          # Python dependencies
├── pytest.ini              # Pytest configuration
└── README.md               # This file
```

## Installation

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd ztb_automation
   ```

2. **Install dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation**:
   ```bash
   python -c "from OS_Mac.library.ztb_installation import ZTBInstaller; print('Installation successful')"
   ```

## Quick Start

### Basic ZTB Component Installation

```python
from OS_Mac.library.ztb_installation import ZTBInstaller
from shared_utils.logger import Logger

# Initialize logger
logger = Logger.initialize_logger("ztb_install.log")

# Create ZTB installer instance
installer = ZTBInstaller(log_handle=logger)

# Install a ZTB component from a DMG (copy .app to Applications)
result = installer.install_component("ZTB-Agent-1.0.0.dmg", installation_mode="copy_app")

if result[0]:
    print(f"Installation successful: {result[1]}")
else:
    print(f"Installation failed: {result[1]}")

# Cleanup any mounted volumes
installer.cleanup_all_mounted_volumes()
```

### Running Installer Packages

```python
# Install using .pkg installer
result = installer.install_component("ZTB-Package-1.0.0.dmg", installation_mode="run_installer")

if result[0]:
    print("Package installation completed")
else:
    print(f"Package installation failed: {result[1]}")
```

### Installation Verification

```python
# Verify that an application was installed correctly
verification_result = installer.verify_installation("MyApp.app")

if verification_result[0]:
    app_info = verification_result[2]
    print(f"App verified: {app_info['app_name']}")
    print(f"Size: {app_info['size']} bytes")
else:
    print(f"Verification failed: {verification_result[1]}")
```

## Running Tests

### Run All Tests

```bash
pytest OS_Mac/test_suite/
```

### Run Smoke Tests Only

```bash
pytest -m smoke OS_Mac/test_suite/
```

### Run Installation Tests

```bash
pytest -m installation OS_Mac/test_suite/
```

### Run with HTML Report

```bash
pytest --html=Reports/test_report.html OS_Mac/test_suite/
```

### Run with Coverage

```bash
pytest --cov=OS_Mac --cov-report=html OS_Mac/test_suite/
```

## Configuration

### Constants Configuration

Edit `shared_utils/constants.py` to customize:

- **File Paths**: ZTB installer archive location, Applications directory, temp directories
- **Timeouts**: Mount, copy, and unmount operation timeouts
- **ZTB Files**: Sample ZTB installer files for testing
- **Error Messages**: Customizable error and success messages

### Logging Configuration

Configure logging in your scripts:

```python
from shared_utils.logger import Logger

# Create logger with custom settings
logger = Logger.initialize_logger(
    log_file_name="my_automation.log",
    log_level="DEBUG",  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    log_dir="/custom/log/directory"
)
```

## API Reference

### ZTBInstaller Class

#### Main Methods

- `install_component(component_dmg_name, installation_mode="copy_app", force_overwrite=False, **kwargs)`

  - Complete Agent installation workflow
  - Returns: `(success, message, installation_details)`

- `mount_dmg(dmg_file_path, mount_point=None)`

  - Mount a DMG file
  - Returns: `(success, message, mount_path)`

- `copy_app_to_applications(app_path, force_overwrite=False)`

  - Copy .app bundle to Applications directory
  - Returns: `(success, message, destination_path)`

- `verify_installation(app_name)`

  - Verify application installation
  - Returns: `(success, message, app_info)`

- `cleanup_all_mounted_volumes()`
  - Cleanup all mounted volumes
  - Returns: `(success, message, cleanup_results)`

#### Installation Modes

- `copy_app`: Copy .app bundles to Applications directory
- `run_installer`: Execute .pkg/.mpkg installer packages
- `custom_script`: Run custom installation scripts

### SystemOps Class

#### System Information

- `get_system_info()`: Get comprehensive system information
- `check_disk_space(path="/", required_space_gb=1)`: Check available disk space
- `is_process_running(process_name)`: Check if process is running

#### Process Management

- `kill_process_by_name(process_name, force=False)`: Kill processes by name
- `run_command(command, timeout=60)`: Execute system commands

#### File Operations

- `create_directory(directory_path, mode=0o755)`: Create directories
- `remove_directory(directory_path, force=False)`: Remove directories
- `get_file_permissions(file_path)`: Get file permissions

### Helpers Class

- `get_file_hash(file_path)`: Calculate hash of a file
- `format_file_size(size_bytes)`: Format file size into human-readable string
- `validate_dmg_file(dmg_path)`: Perform basic validation on a DMG file

## Best Practices

1. **Always use try-catch blocks** when calling automation methods
2. **Clean up mounted volumes** after operations using `cleanup_all_mounted_volumes()`
3. **Verify installations** using `verify_installation()` method
4. **Use appropriate logging levels** for different environments
5. **Test with sample Agent files** before using with production files
6. **Check disk space** before large installations
7. **Use force_overwrite carefully** to avoid accidental data loss

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**

   - Ensure the script has necessary permissions
   - Use `sudo` for system-level operations when required

2. **DMG Mount Failures**

   - Verify Agent file integrity
   - Check available disk space
   - Ensure no other processes are using the DMG

3. **Application Copy Failures**
   - Check Applications directory permissions
   - Verify sufficient disk space
   - Ensure target application doesn't exist (or use force_overwrite)

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
logger = Logger.initialize_logger("debug.log", log_level="DEBUG")
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Check the troubleshooting section
- Review test cases for usage examples
- Create an issue in the repository
