#!/usr/bin/env python3
################################################################
#               ZTB AUTOMATION USAGE EXAMPLES                 #
#                                                              #
# AUTHOR : ZTB Automation Framework                           #
################################################################

"""
This script demonstrates various usage patterns for the Agent Automation Framework.
Run individual examples by calling the respective functions.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from OS_Mac.library.agent_installation import ZTBInstaller
from OS_Mac.library.system_ops import SystemOps
from shared_utils.common.logger import Logger
from shared_utils.helpers import Helpers


def example_basic_installation():
    """
    Example 1: Basic ZTB component installation with application copying
    """
    print("=" * 60)
    print("Example 1: Basic ZTB Component Installation")
    print("=" * 60)
    
    # Initialize logger
    logger = Logger.initialize_logger("basic_install_example.log", log_level="INFO")
    
    try:
        # Create ZTB installer instance
        installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        
        # Example ZTB component DMG file (you would replace this with your actual file)
        component_dmg = "MyApplication-1.0.0.dmg"
        
        print(f"Installing component from: {component_dmg}")
        
        # Install the component (copy .app to Applications)
        result = installer.install_component(
            component_dmg_name=component_dmg,
            installation_mode="copy_app",
            force_overwrite=False
        )
        
        if result[0]:
            print(f"✅ Installation successful: {result[1]}")
            installation_details = result[2]
            print(f"   Installer file: {installation_details.get('dmg_file', 'N/A')}")
            print(f"   Mount path: {installation_details.get('mount_path', 'N/A')}")
            print(f"   Copied apps: {installation_details.get('copied_apps', [])}")
        else:
            print(f"❌ Installation failed: {result[1]}")
        
        # Always cleanup mounted volumes
        cleanup_result = installer.cleanup_all_mounted_volumes()
        if cleanup_result[0]:
            print("✅ Cleanup completed successfully")
        else:
            print(f"⚠️  Cleanup warning: {cleanup_result[1]}")
            
    except Exception as e:
        print(f"❌ Error during installation: {e}")


def example_package_installation():
    """
    Example 2: Installing a ZTB component with .pkg installer packages
    """
    print("=" * 60)
    print("Example 2: Package Installation")
    print("=" * 60)
    
    logger = Logger.initialize_logger("package_install_example.log", log_level="INFO")
    
    try:
        installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        
        component_dmg = "MyPackageInstaller-2.0.0.dmg"
        
        print(f"Installing package from: {component_dmg}")
        
        # Install using package installer mode
        result = installer.install_component(
            component_dmg_name=component_dmg,
            installation_mode="run_installer"
        )
        
        if result[0]:
            print(f"✅ Package installation successful: {result[1]}")
            installation_details = result[2]
            print(f"   Installed packages: {installation_details.get('installed_packages', [])}")
        else:
            print(f"❌ Package installation failed: {result[1]}")
        
        installer.cleanup_all_mounted_volumes()
        
    except Exception as e:
        print(f"❌ Error during package installation: {e}")


def example_installation_verification():
    """
    Example 3: Verifying application installation
    """
    print("=" * 60)
    print("Example 3: Installation Verification")
    print("=" * 60)
    
    logger = Logger.initialize_logger("verification_example.log", log_level="INFO")
    
    try:
        installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        
        # List of applications to verify
        apps_to_verify = [
            "MyApplication.app",
            "TextEdit.app",  # System app that should exist
            "NonExistentApp.app"
        ]
        
        for app_name in apps_to_verify:
            print(f"\nVerifying: {app_name}")
            
            result = installer.verify_installation(app_name)
            
            if result[0]:
                app_info = result[2]
                print(f"✅ Verification successful")
                print(f"   Path: {app_info['app_path']}")
                print(f"   Size: {Helpers.format_file_size(app_info['size'])}")
            else:
                print(f"❌ Verification failed: {result[1]}")
                
    except Exception as e:
        print(f"❌ Error during verification: {e}")


def example_system_operations():
    """
    Example 4: System operations and information gathering
    """
    print("=" * 60)
    print("Example 4: System Operations")
    print("=" * 60)
    
    logger = Logger.initialize_logger("system_ops_example.log", log_level="INFO")
    
    try:
        system_ops = SystemOps(log_handle=logger)
        
        # Get system information
        print("System Information:")
        system_info = system_ops.get_system_info()
        
        key_info = [
            "platform", "system", "release", "machine", 
            "cpu_count", "current_user"
        ]
        
        for key in key_info:
            if key in system_info:
                value = system_info[key]
                if key in ["memory_total", "memory_available"]:
                    value = Helpers.format_file_size(value)
                print(f"   {key}: {value}")
        
        # Check disk space
        print(f"\nDisk Space Check:")
        disk_result = system_ops.check_disk_space("/", required_space_gb=1.0)
        if disk_result[0]:
            print(f"✅ Sufficient disk space available")
            print(f"   Available: {disk_result[1]:.2f} GB")
        else:
            print(f"❌ Insufficient disk space")
            print(f"   Available: {disk_result[1]:.2f} GB, Required: {disk_result[2]} GB")
        
        # Check for a common process
        print(f"\nProcess Check:")
        process_result = system_ops.is_process_running("Finder")
        if process_result[0]:
            print(f"✅ Finder process is running ({len(process_result[1])} instances)")
        else:
            print(f"❌ Finder process not found")
        
        # Run a simple command
        print(f"\nCommand Execution:")
        cmd_result = system_ops.run_command(["echo", "Hello from ZTB Automation!"])
        if cmd_result[0]:
            print(f"✅ Command executed successfully")
            print(f"   Output: {cmd_result[2].strip()}")
        else:
            print(f"❌ Command failed: {cmd_result[3]}")
            
    except Exception as e:
        print(f"❌ Error during system operations: {e}")


def example_helper_functions():
    """
    Example 5: Using helper functions
    """
    print("=" * 60)
    print("Example 5: Helper Functions")
    print("=" * 60)
    
    try:
        # File size formatting
        sizes = [512, 1024, 1048576, 1073741824, 1099511627776]
        print("File Size Formatting:")
        for size in sizes:
            formatted = Helpers.format_file_size(size)
            print(f"   {size} bytes = {formatted}")
        
        # Timestamp generation
        print(f"\nTimestamp Generation:")
        timestamp = Helpers.get_timestamp()
        print(f"   Current timestamp: {timestamp}")
        
        custom_format = Helpers.get_timestamp("%Y-%m-%d %H:%M:%S")
        print(f"   Custom format: {custom_format}")
        
        # DMG file validation
        print(f"\nDMG File Validation:")
        test_files = [
            "/path/to/valid.dmg",
            "/path/to/invalid.txt",
            "/non/existent/file.dmg"
        ]
        
        for file_path in test_files:
            result = Helpers.validate_dmg_file(file_path)
            status = "✅" if result[0] else "❌"
            print(f"   {status} {file_path}: {result[1]}")
        
        # Temporary directory operations
        print(f"\nTemporary Directory Operations:")
        temp_dir = Helpers.create_temp_directory("dmg_example_")
        if temp_dir:
            print(f"✅ Created temp directory: {temp_dir}")
            
            # Create a test file
            test_file = os.path.join(temp_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("Test content")
            print(f"   Created test file: {test_file}")
            
            # Cleanup
            cleanup_success = Helpers.cleanup_temp_directory(temp_dir)
            if cleanup_success:
                print(f"✅ Cleaned up temp directory")
            else:
                print(f"❌ Failed to cleanup temp directory")
        else:
            print(f"❌ Failed to create temp directory")
            
    except Exception as e:
        print(f"❌ Error with helper functions: {e}")


def example_error_handling():
    """
    Example 6: Error handling and recovery
    """
    print("=" * 60)
    print("Example 6: Error Handling")
    print("=" * 60)
    
    logger = Logger.initialize_logger("error_handling_example.log", log_level="DEBUG")
    
    try:
        installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        
        # Attempt to install non-existent component
        print("Testing error handling with non-existent component:")
        result = installer.install_component("non_existent_file.dmg")
        
        if not result[0]:
            print(f"✅ Error properly handled: {result[1]}")
        else:
            print(f"❌ Unexpected success")
        
        # Test with invalid installation mode
        print(f"\nTesting invalid installation mode:")
        result = installer.install_component("test.dmg", installation_mode="invalid_mode")
        
        if not result[0]:
            print(f"✅ Invalid mode properly handled: {result[1]}")
        else:
            print(f"❌ Unexpected success")
        
        # Test cleanup when no volumes are mounted
        print(f"\nTesting cleanup with no mounted volumes:")
        cleanup_result = installer.cleanup_all_mounted_volumes()
        
        if cleanup_result[0]:
            print(f"✅ Cleanup handled gracefully: {cleanup_result[1]}")
        else:
            print(f"❌ Unexpected cleanup failure")
            
    except Exception as e:
        print(f"✅ Exception properly caught: {e}")


def main():
    """
    Main function to run all examples
    """
    print("ZTB Automation Framework - Usage Examples")
    print("=" * 60)
    
    examples = [
        ("Basic Installation", example_basic_installation),
        ("Package Installation", example_package_installation),
        ("Installation Verification", example_installation_verification),
        ("System Operations", example_system_operations),
        ("Helper Functions", example_helper_functions),
        ("Error Handling", example_error_handling)
    ]
    
    for name, example_func in examples:
        try:
            example_func()
            print(f"\n✅ {name} example completed\n")
        except Exception as e:
            print(f"\n❌ {name} example failed: {e}\n")
    
    print("=" * 60)
    print("All examples completed!")
    print("=" * 60)


if __name__ == "__main__":
    main()
