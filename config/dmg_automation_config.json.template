{"ztb_automation": {"description": "Configuration template for Agent Automation Framework", "version": "1.0.0", "paths": {"dmg_archive": "./OS_Mac/resource", "applications": "/Applications", "temp_directory": "/tmp/ztb_automation", "log_directory": "./Reports/logs", "test_reports": "./Reports/test_results"}, "timeouts": {"dmg_mount_timeout": 30, "app_copy_timeout": 120, "dmg_unmount_timeout": 30, "verification_timeout": 60, "command_timeout": 60}, "installation": {"default_mode": "copy_app", "force_overwrite": false, "verify_after_install": true, "cleanup_on_failure": true, "backup_existing_apps": false}, "logging": {"level": "INFO", "console_output": true, "file_output": true, "max_log_files": 10, "log_rotation_size_mb": 50}, "verification": {"check_app_bundle": true, "check_info_plist": true, "check_executable": true, "check_code_signature": false, "minimum_app_size_bytes": 1024}, "system_requirements": {"minimum_disk_space_gb": 1.0, "required_macos_version": "10.14", "check_system_integrity": true}, "dmg_files": {"sample_apps": [{"name": "TestApp-1.0.0.dmg", "description": "Sample test application", "installation_mode": "copy_app", "expected_apps": ["TestApp.app"]}, {"name": "SampleUtility-2.1.0.dmg", "description": "Sample utility with installer", "installation_mode": "run_installer", "expected_packages": ["SampleUtility.pkg"]}]}, "testing": {"test_data_directory": "./OS_Mac/resource/test_data", "create_mock_dmg_files": true, "cleanup_after_tests": true, "parallel_test_execution": false, "test_timeout_seconds": 300}, "security": {"verify_dmg_signatures": false, "allow_unsigned_dmg": true, "quarantine_handling": "auto", "gatekeeper_bypass": false}, "notifications": {"enable_notifications": false, "notification_types": ["success", "failure", "warning"], "email_notifications": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "recipients": []}}, "advanced": {"retry_attempts": 3, "retry_delay_seconds": 5, "concurrent_installations": 1, "memory_limit_mb": 1024, "temp_file_cleanup_age_hours": 24}}}