2025-07-30 20:07:00,766 - ui_automation_test.log - INFO - initialize_logger:60 - Logger initialized: ui_automation_test.log
2025-07-30 20:07:00,766 - ui_automation_test.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/Reports/logs/ui_automation_test.log_20250730_200700.log
2025-07-30 20:07:00,766 - ui_automation_test.log - INFO - __init__:51 - ZTB Installer initialized
2025-07-30 20:07:00,766 - ui_automation_test.log - INFO - mount_dmg:107 - Mounting DMG file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 20:07:00,832 - ui_automation_test.log - DEBUG - mount_dmg:131 - hdiutil stdout: /dev/disk6          	GUID_partition_scheme          	
/dev/disk6s1        	Apple_APFS                     	
/dev/disk7          	EF57347C-0000-11AA-AA11-0030654	
/dev/disk7s1        	*************-11AA-AA11-0030654	/Volumes/Zscaler Zero Trust Browser Installer 1

2025-07-30 20:07:00,832 - ui_automation_test.log - DEBUG - mount_dmg:132 - hdiutil stderr: 
2025-07-30 20:07:00,832 - ui_automation_test.log - INFO - mount_dmg:136 - DMG mounted successfully at: /Volumes/Zscaler Zero Trust Browser Installer 1
2025-07-30 20:07:03,840 - ui_automation_test.log - ERROR - find_installer_window:83 - Error finding installer window: module 'pygetwindow' has no attribute 'getWindowsWithTitle'
2025-07-30 20:07:05,845 - ui_automation_test.log - INFO - unmount_dmg:301 - Unmounting Agent: /Volumes/Zscaler Zero Trust Browser Installer 1
2025-07-30 20:07:05,959 - ui_automation_test.log - INFO - unmount_dmg:315 - Successfully unmounted: /Volumes/Zscaler Zero Trust Browser Installer 1
