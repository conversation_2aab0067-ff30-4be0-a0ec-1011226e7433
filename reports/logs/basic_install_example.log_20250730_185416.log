2025-07-30 18:54:16,906 - basic_install_example.log - INFO - initialize_logger:60 - Logger initialized: basic_install_example.log
2025-07-30 18:54:16,906 - basic_install_example.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/Reports/logs/basic_install_example.log_20250730_185416.log
2025-07-30 18:54:16,906 - basic_install_example.log - INFO - __init__:51 - ZTB Installer initialized
2025-07-30 18:54:16,906 - basic_install_example.log - INFO - install_component:331 - Starting ZTB component installation: ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 18:54:16,906 - basic_install_example.log - INFO - mount_dmg:107 - Mounting DMG file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 18:54:46,916 - basic_install_example.log - ERROR - mount_dmg:139 - DMG mount operation timed out
