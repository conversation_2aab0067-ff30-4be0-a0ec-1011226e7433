2025-07-30 18:57:16,495 - basic_install_example.log - INFO - initialize_logger:60 - Logger initialized: basic_install_example.log
2025-07-30 18:57:16,495 - basic_install_example.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/Reports/logs/basic_install_example.log_20250730_185716.log
2025-07-30 18:57:16,495 - basic_install_example.log - INFO - __init__:51 - ZTB Installer initialized
2025-07-30 18:57:16,495 - basic_install_example.log - INFO - install_component:336 - Starting ZTB component installation: ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 18:57:16,495 - basic_install_example.log - INFO - mount_dmg:107 - Mounting DMG file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 18:57:16,565 - basic_install_example.log - INFO - mount_dmg:136 - DMG mounted successfully at: /Volumes/Zscaler Zero Trust Browser Installer
2025-07-30 18:57:16,566 - basic_install_example.log - INFO - run_installer_package:259 - Running installer package: /Volumes/Zscaler Zero Trust Browser Installer/ZscalerZTBSetup-arm64-0.0.1.pkg
2025-07-30 18:59:16,573 - basic_install_example.log - ERROR - run_installer_package:278 - Installer package operation timed out
2025-07-30 18:59:16,573 - basic_install_example.log - INFO - unmount_dmg:295 - Unmounting Agent: /Volumes/Zscaler Zero Trust Browser Installer
2025-07-30 18:59:16,786 - basic_install_example.log - INFO - unmount_dmg:309 - Successfully unmounted: /Volumes/Zscaler Zero Trust Browser Installer
