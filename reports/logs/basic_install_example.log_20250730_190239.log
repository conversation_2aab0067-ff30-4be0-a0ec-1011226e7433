2025-07-30 19:02:39,146 - basic_install_example.log - INFO - initialize_logger:60 - Logger initialized: basic_install_example.log
2025-07-30 19:02:39,146 - basic_install_example.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/Reports/logs/basic_install_example.log_20250730_190239.log
2025-07-30 19:02:39,146 - basic_install_example.log - INFO - __init__:51 - ZTB Installer initialized
2025-07-30 19:02:39,146 - basic_install_example.log - INFO - install_component:342 - Starting ZTB component installation: ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 19:02:39,146 - basic_install_example.log - INFO - mount_dmg:107 - Mounting DMG file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 19:02:39,611 - basic_install_example.log - INFO - mount_dmg:136 - DMG mounted successfully at: /Volumes/Zscaler Zero Trust Browser Installer
2025-07-30 19:02:39,612 - basic_install_example.log - INFO - run_installer_package:259 - Running installer package: /Volumes/Zscaler Zero Trust Browser Installer/ZscalerZTBSetup-arm64-0.0.1.pkg
2025-07-30 19:02:39,847 - basic_install_example.log - ERROR - run_installer_package:280 - Installer failed: installer: Must be run as root to install this package.

2025-07-30 19:02:39,847 - basic_install_example.log - INFO - unmount_dmg:301 - Unmounting Agent: /Volumes/Zscaler Zero Trust Browser Installer
2025-07-30 19:02:39,976 - basic_install_example.log - INFO - unmount_dmg:315 - Successfully unmounted: /Volumes/Zscaler Zero Trust Browser Installer
