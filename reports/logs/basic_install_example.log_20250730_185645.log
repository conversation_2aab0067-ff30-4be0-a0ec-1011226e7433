2025-07-30 18:56:45,003 - basic_install_example.log - INFO - initialize_logger:60 - Logger initialized: basic_install_example.log
2025-07-30 18:56:45,003 - basic_install_example.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/Reports/logs/basic_install_example.log_20250730_185645.log
2025-07-30 18:56:45,003 - basic_install_example.log - INFO - __init__:51 - ZTB Installer initialized
2025-07-30 18:56:45,003 - basic_install_example.log - INFO - install_component:334 - Starting ZTB component installation: ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 18:56:45,003 - basic_install_example.log - INFO - mount_dmg:107 - Mounting DMG file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 18:56:45,061 - basic_install_example.log - INFO - mount_dmg:136 - DMG mounted successfully at: /Volumes/Zscaler
2025-07-30 18:56:45,062 - basic_install_example.log - INFO - unmount_dmg:293 - Unmounting Agent: /Volumes/Zscaler
2025-07-30 18:56:45,106 - basic_install_example.log - ERROR - unmount_dmg:310 - Failed to unmount DMG: hdiutil: detach failed - No such file or directory

2025-07-30 18:56:45,106 - basic_install_example.log - WARNING - install_component:393 - Failed to unmount DMG: Failed to unmount DMG: hdiutil: detach failed - No such file or directory

2025-07-30 18:56:45,106 - basic_install_example.log - ERROR - install_component:396 - Error during ZTB component installation: [Errno 2] No such file or directory: '/Volumes/Zscaler'
2025-07-30 18:56:45,106 - basic_install_example.log - INFO - unmount_dmg:293 - Unmounting Agent: /Volumes/Zscaler
2025-07-30 18:56:45,129 - basic_install_example.log - ERROR - unmount_dmg:310 - Failed to unmount DMG: hdiutil: detach failed - No such file or directory

