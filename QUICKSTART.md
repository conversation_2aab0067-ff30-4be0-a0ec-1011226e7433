# Quick Start Guide - ZTB Automation Framework

This guide will help you get started with the ZTB Automation Framework in just a few minutes.

## Prerequisites

- macOS 10.14 or later
- Python 3.7 or later
- Administrative privileges (for some operations)

## 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd ztb_automation

# Install dependencies
pip install -r requirements.txt

# Verify installation
python -c "from OS_Mac.library.ztb_installation import ZTBInstaller; from shared_utils.logger import Logger; print('✅ Installation successful')"
```

## 2. Prepare Your Agent Files

Place your Agent files in the `OS_Mac/resource/` directory:

```bash
# Example structure
OS_Mac/resource/
├── MyApp-1.0.0.dmg
├── AnotherApp-2.1.0.dmg
└── test_data/
    └── sample_files...
```

## 3. Basic Usage

### Simple Application Installation

Create a file called `install_app.py`:

```python
#!/usr/bin/env python3
from OS_Mac.library.ztb_installation import ZTBInstaller
from shared_utils.logger import Logger

# Initialize
logger = Logger.initialize_logger("install.log")
installer = ZTBInstaller(log_handle=logger)

# Install your ZTB component
result = installer.install_component("MyApp-1.0.0.dmg")

if result[0]:
    print("✅ Installation successful!")
    print(f"Details: {result[2]}")
else:
    print(f"❌ Installation failed: {result[1]}")

# Cleanup
installer.cleanup_all_mounted_volumes()
```

Run it:

```bash
python install_app.py
```

### Verify Installation

```python
# Verify the app was installed correctly
verification = installer.verify_installation("MyApp.app")

if verification[0]:
    print("✅ App verified successfully!")
    app_info = verification[2]
    print(f"Location: {app_info['app_path']}")
    print(f"Size: {app_info['size']} bytes")
else:
    print(f"❌ Verification failed: {verification[1]}")
```

## 4. Running Tests

### Quick Smoke Test

```bash
# Run basic functionality tests
pytest -m smoke OS_Mac/test_suite/
```

### Full Test Suite

```bash
# Run all tests with HTML report
pytest --html=Reports/test_report.html OS_Mac/test_suite/
```

### Test Specific Functionality

```bash
# Test only installation features
pytest -m installation OS_Mac/test_suite/

# Test only verification features
pytest -m verification OS_Mac/test_suite/
```

## 5. Common Use Cases

### Case 1: Install Multiple Apps

```python
component_dmgs = ["App1.dmg", "App2.dmg", "App3.dmg"]

for dmg in component_dmgs:
    print(f"Installing {dmg}...")
    result = installer.install_component(dmg)

    if result[0]:
        print(f"✅ {dmg} installed successfully")
    else:
        print(f"❌ {dmg} failed: {result[1]}")
```

### Case 2: Install with Package Installer

```python
# For installers containing .pkg files
result = installer.install_component(
    "MyPackage.dmg",
    installation_mode="run_installer"
)
```

### Case 3: Force Overwrite Existing App

```python
# Overwrite existing application
result = installer.install_agent(
    "MyApp.dmg",
    force_overwrite=True
)
```

## 6. Configuration

Copy and customize the configuration template:

```bash
cp config/ztb_automation_config.json.template config/my_config.json
# Edit my_config.json with your settings
```

## 7. Logging and Debugging

### Enable Debug Logging

```python
logger = Logger.initialize_logger(
    "debug.log",
    log_level="DEBUG"
)
```

### View Logs

```bash
# View recent logs
tail -f Reports/logs/install.log_*

# View all log files
ls -la Reports/logs/
```

## 8. Troubleshooting

### Common Issues

**Permission Denied**

```bash
# Run with sudo if needed
sudo python install_app.py
```

**Agent Won't Mount**

```bash
# Check Agent file integrity
hdiutil verify MyApp.dmg

# Check available disk space
df -h
```

**App Copy Failed**

```bash
# Check Applications directory permissions
ls -la /Applications/

# Ensure sufficient disk space
du -sh /Applications/
```

### Debug Mode

```python
# Enable verbose logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Run your installation code
```

## 9. Next Steps

- Read the full [README.md](README.md) for comprehensive documentation
- Explore [examples.py](examples.py) for more usage patterns
- Check the test files for advanced usage examples
- Customize configuration in `config/` directory

## 10. Getting Help

- Check the logs in `Reports/logs/` for detailed error information
- Run tests to verify your setup: `pytest -v OS_Mac/test_suite/`
- Review the API documentation in the main README
- Look at existing test cases for usage examples

## Quick Reference

### Essential Commands

```bash
# Install dependencies
pip install -r requirements.txt

# Run smoke tests
pytest -m smoke

# Install a Agent (Python)
python -c "
from OS_Mac.library.dmg_installation import AgentInstaller
installer = AgentInstaller()
result = installer.install_dmg('your_app.dmg')
print('Success!' if result[0] else f'Failed: {result[1]}')
installer.cleanup_all_mounted_volumes()
"

# Generate test report
pytest --html=Reports/test_report.html OS_Mac/test_suite/
```

### Key Classes

- `AgentInstaller`: Main class for Agent operations
- `SystemOps`: System information and operations
- `Logger`: Logging utilities
- `HelperFunctions`: Utility functions

You're now ready to automate Agent installations! 🚀
