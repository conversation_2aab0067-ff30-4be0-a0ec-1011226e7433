################################################################
#               ZTB INSTALLATION AUTOMATION LIBRARY           #
#                                                              #
# AUTHOR : ZTB Automation Framework                           #
################################################################

import subprocess
import os
import time
import shutil
import re
from pathlib import Path
from shared_utils.common import constants
from shared_utils.common.logger import Logger


class ZTBInstaller:
    """
    Core class for ZTB component installation automation on macOS
    
    This class provides comprehensive functionality for:
    - Mounting DMG files
    - Copying applications to /Applications
    - Running installer packages
    - Verifying installations
    - Cleaning up mounted volumes
    """

    def __init__(self, verify_dmg_exists=False, log_handle=None):
        """
        Initialize ZTB installer
        
        Args:
            verify_dmg_exists (bool): Verify DMG files exist during initialization
            log_handle: Existing logger handle, creates new if None
        """
        self.const = constants.Utils
        if log_handle is None:
            self.logger = Logger.initialize_logger("ZTB_Installer.log", log_level="DEBUG")
        else:
            self.logger = log_handle
            
        self.ztb_archive_path = self.const.ZTB_ARCHIVE_PATH
        self.mounted_volumes = []  # Track mounted volumes for cleanup
        self.installed_applications = []  # Track installed apps for verification
        
        if verify_dmg_exists:
            assert self.verify_dmg_installers_exist()[0]
            
        assert os.path.exists(self.ztb_archive_path)
        self.logger.info("ZTB Installer initialized")

    def list_available_dmg_installers(self):
        """
        List all available DMG installers in the resource directory
        
        Returns:
            tuple: (success, message, dmg_files_list)
        """
        try:
            dmg_files = [f for f in os.listdir(self.ztb_archive_path) if f.endswith('.dmg')]
            
            if len(dmg_files) == 0:
                self.logger.error("No DMG files found in resource directory")
                return (False, "Error: No DMG files found", None)
                
            self.logger.info(f"Found {len(dmg_files)} DMG files: {dmg_files}")
            return (True, "Success: DMG files found", dmg_files)
            
        except Exception as e:
            self.logger.error(f"Error listing DMG files: {e}")
            return (False, f"Error listing DMG files: {e}", None)

    def verify_dmg_installers_exist(self):
        """
        Verify that required DMG installer files exist in the resource directory
        
        Returns:
            tuple: (success, message, None)
        """
        try:
            if not os.path.exists(self.ztb_archive_path):
                return (False, "Error: ZTB archive path does not exist", None)
                
            dmg_files = self.list_available_dmg_installers()
            if not dmg_files[0]:
                return dmg_files
                
            return (True, "Success: DMG installer files verified", None)
            
        except Exception as e:
            self.logger.error(f"Error verifying DMG files: {e}")
            return (False, f"Error verifying DMG files: {e}", None)

    def mount_dmg(self, dmg_file_path, mount_point=None):
        """
        Mount a DMG file
        
        Args:
            dmg_file_path (str): Path to the DMG file
            mount_point (str): Optional custom mount point
            
        Returns:
            tuple: (success, message, mount_path)
        """
        try:
            self.logger.info(f"Mounting DMG file: {dmg_file_path}")
            
            if not os.path.exists(dmg_file_path):
                return (False, f"DMG file not found: {dmg_file_path}", None)
            
            # Use hdiutil to mount the Agent with automatic license agreement acceptance
            if mount_point:
                mount_command = f'echo "Y" | hdiutil attach "{dmg_file_path}" -mountpoint "{mount_point}" -nobrowse'
            else:
                mount_command = f'echo "Y" | hdiutil attach "{dmg_file_path}" -nobrowse'

            result = subprocess.run(
                mount_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=self.const.MOUNT_TIMEOUT
            )
            
            if result.returncode != 0:
                self.logger.error(f"Failed to mount DMG: {result.stderr}")
                return (False, f"Failed to mount DMG: {result.stderr}", None)
            
            # Extract mount path from output
            self.logger.debug(f"hdiutil stdout: {result.stdout}")
            self.logger.debug(f"hdiutil stderr: {result.stderr}")
            mount_path = self._extract_mount_path(result.stdout)
            if mount_path:
                self.mounted_volumes.append(mount_path)
                self.logger.info(f"DMG mounted successfully at: {mount_path}")
                return (True, "DMG mounted successfully", mount_path)
            else:
                return (False, "Could not determine mount path", None)
                
        except subprocess.TimeoutExpired:
            self.logger.error("DMG mount operation timed out")
            return (False, "DMG mount operation timed out", None)
        except Exception as e:
            self.logger.error(f"Error mounting DMG: {e}")
            return (False, f"Error mounting DMG: {e}", None)

    def _extract_mount_path(self, hdiutil_output):
        """
        Extract mount path from hdiutil output

        Args:
            hdiutil_output (str): Output from hdiutil attach command

        Returns:
            str: Mount path or None if not found
        """
        try:
            # hdiutil output format: /dev/diskXsY    Apple_HFS    /Volumes/VolumeName
            lines = hdiutil_output.strip().split('\n')
            for line in lines:
                if '/Volumes/' in line:
                    # Find the start of the /Volumes/ path
                    volumes_index = line.find('/Volumes/')
                    if volumes_index != -1:
                        # Extract everything from /Volumes/ to the end of the line
                        mount_path = line[volumes_index:].strip()
                        return mount_path
            return None
        except Exception as e:
            self.logger.error(f"Error extracting mount path: {e}")
            return None

    def find_app_in_dmg(self, mount_path):
        """
        Find .app bundles in the mounted DMG

        Args:
            mount_path (str): Path to mounted DMG volume

        Returns:
            tuple: (success, message, app_paths_list)
        """
        try:
            if not os.path.exists(mount_path):
                return (False, f"Mount path does not exist: {mount_path}", None)

            app_paths = []
            for item in os.listdir(mount_path):
                item_path = os.path.join(mount_path, item)
                if item.endswith('.app') and os.path.isdir(item_path):
                    app_paths.append(item_path)
                    self.logger.info(f"Found application: {item}")

            if not app_paths:
                return (False, "No .app bundles found in DMG", None)

            return (True, f"Found {len(app_paths)} applications", app_paths)

        except Exception as e:
            self.logger.error(f"Error finding apps in DMG: {e}")
            return (False, f"Error finding apps in DMG: {e}", None)

    def copy_app_to_applications(self, app_path, force_overwrite=False):
        """
        Copy application bundle to /Applications directory

        Args:
            app_path (str): Path to the .app bundle
            force_overwrite (bool): Overwrite existing application

        Returns:
            tuple: (success, message, destination_path)
        """
        try:
            app_name = os.path.basename(app_path)
            destination_path = os.path.join(self.const.APPLICATIONS_PATH, app_name)

            self.logger.info(f"Copying {app_name} to Applications folder")

            # Check if app already exists
            if os.path.exists(destination_path):
                if not force_overwrite:
                    return (False, f"Application already exists: {app_name}", None)
                else:
                    self.logger.info(f"Removing existing application: {app_name}")
                    shutil.rmtree(destination_path)

            # Copy the application
            shutil.copytree(app_path, destination_path)

            # Verify the copy was successful
            if os.path.exists(destination_path):
                self.installed_applications.append(destination_path)
                self.logger.info(f"Successfully copied {app_name} to Applications")
                return (True, f"Application copied successfully: {app_name}", destination_path)
            else:
                return (False, f"Copy verification failed for: {app_name}", None)

        except Exception as e:
            self.logger.error(f"Error copying application: {e}")
            return (False, f"Error copying application: {e}", None)

    def run_installer_package(self, pkg_path, target="/"):
        """
        Run an installer package (.pkg or .mpkg)

        Args:
            pkg_path (str): Path to the installer package
            target (str): Installation target directory

        Returns:
            tuple: (success, message, None)
        """
        try:
            if not os.path.exists(pkg_path):
                return (False, f"Installer package not found: {pkg_path}", None)

            self.logger.info(f"Running installer package: {pkg_path}")

            install_command = ["sudo", "installer", "-pkg", pkg_path, "-target", target]

            result = subprocess.run(
                install_command,
                capture_output=True,
                text=True,
                timeout=self.const.APP_COPY_TIMEOUT
            )

            if result.returncode == 0:
                self.logger.info("Installer package completed successfully")
                return (True, "Installer package completed successfully", None)
            else:
                self.logger.error(f"Installer failed: {result.stderr}")
                return (False, f"Installer failed: {result.stderr}", None)

        except subprocess.TimeoutExpired:
            self.logger.error("Installer package operation timed out")
            return (False, "Installer package operation timed out", None)
        except Exception as e:
            self.logger.error(f"Error running installer package: {e}")
            return (False, f"Error running installer package: {e}", None)

    def unmount_dmg(self, mount_path):
        """
        Unmount a DMG volume

        Args:
            mount_path (str): Path to the mounted volume, typically in /Volumes/

        Returns:
            tuple: (success, message, None)
        """
        try:
            self.logger.info(f"Unmounting Agent: {mount_path}")

            unmount_command = ["hdiutil", "detach", mount_path]

            result = subprocess.run(
                unmount_command,
                capture_output=True,
                text=True,
                timeout=self.const.UNMOUNT_TIMEOUT
            )

            if result.returncode == 0:
                if mount_path in self.mounted_volumes:
                    self.mounted_volumes.remove(mount_path)
                self.logger.info(f"Successfully unmounted: {mount_path}")
                return (True, "DMG unmounted successfully", None)
            else:
                self.logger.error(f"Failed to unmount DMG: {result.stderr}")
                return (False, f"Failed to unmount DMG: {result.stderr}", None)

        except subprocess.TimeoutExpired:
            self.logger.error("DMG unmount operation timed out")
            return (False, "DMG unmount operation timed out", None)
        except Exception as e:
            self.logger.error(f"Error unmounting DMG: {e}")
            return (False, f"Error unmounting DMG: {e}", None)

    def install_component(self, component_dmg_name, installation_mode="copy_app", force_overwrite=False, **kwargs):
        """
        Complete ZTB component installation workflow from a DMG file.

        Args:
            component_dmg_name (str): Name of DMG file in resource directory
            installation_mode (str): Installation mode ('copy_app', 'run_installer', 'custom_script')
            force_overwrite (bool): Overwrite existing applications
            **kwargs: Additional parameters for specific installation modes

        Returns:
            tuple: (success, message, installation_details)
        """
        try:
            self.logger.info(f"Starting ZTB component installation: {component_dmg_name}")

            # Construct full path to Agent file
            dmg_path = os.path.join(self.ztb_archive_path, component_dmg_name)

            if not os.path.exists(dmg_path):
                return (False, f"Agent file not found: {dmg_path}", None)

            # Mount the Agent
            mount_result = self.mount_dmg(dmg_path)
            if not mount_result[0]:
                return mount_result

            mount_path = mount_result[2]
            installation_details = {"dmg_file": component_dmg_name, "mount_path": mount_path}

            try:
                if installation_mode == "copy_app":
                    # Find and copy applications
                    apps_result = self.find_app_in_dmg(mount_path)
                    if not apps_result[0]:
                        return apps_result

                    app_paths = apps_result[2]
                    copied_apps = []

                    for app_path in app_paths:
                        copy_result = self.copy_app_to_applications(app_path, force_overwrite)
                        if copy_result[0]:
                            copied_apps.append(copy_result[2])
                        else:
                            self.logger.warning(f"Failed to copy app: {copy_result[1]}")

                    installation_details["copied_apps"] = copied_apps

                elif installation_mode == "run_installer":
                    # Find and run installer packages
                    pkg_files = [f for f in os.listdir(mount_path) if f.endswith(('.pkg', '.mpkg'))]

                    if not pkg_files:
                        return (False, "No installer packages found in Agent", None)

                    for pkg_file in pkg_files:
                        pkg_path = os.path.join(mount_path, pkg_file)
                        install_result = self.run_installer_package(pkg_path)
                        if not install_result[0]:
                            return install_result

                    installation_details["installed_packages"] = pkg_files

                else:
                    return (False, f"Unsupported installation mode: {installation_mode}", None)

                return (True, "ZTB component installation completed successfully", installation_details)

            finally:
                # Always try to unmount the Agent
                unmount_result = self.unmount_dmg(mount_path)
                if not unmount_result[0]:
                    self.logger.warning(f"Failed to unmount DMG: {unmount_result[1]}")

        except Exception as e:
            self.logger.error(f"Error during ZTB component installation: {e}")
            return (False, f"Error during ZTB component installation: {e}", None)

    def verify_installation(self, app_name):
        """
        Verify that an application was installed successfully

        Args:
            app_name (str): Name of the application to verify

        Returns:
            tuple: (success, message, app_info)
        """
        try:
            app_path = os.path.join(self.const.APPLICATIONS_PATH, app_name)

            if not os.path.exists(app_path):
                return (False, f"Application not found: {app_name}", None)

            # Check if it's a valid app bundle
            if not app_name.endswith('.app'):
                return (False, f"Not a valid app bundle: {app_name}", None)

            # Check for Info.plist
            info_plist_path = os.path.join(app_path, "Contents", "Info.plist")
            if not os.path.exists(info_plist_path):
                return (False, f"Invalid app bundle - missing Info.plist: {app_name}", None)

            app_info = {
                "app_name": app_name,
                "app_path": app_path,
                "info_plist": info_plist_path,
                "size": self._get_directory_size(app_path)
            }

            self.logger.info(f"Application verification successful: {app_name}")
            return (True, "Application verification successful", app_info)

        except Exception as e:
            self.logger.error(f"Error verifying installation: {e}")
            return (False, f"Error verifying installation: {e}", None)

    def cleanup_all_mounted_volumes(self):
        """
        Cleanup all mounted volumes tracked by this instance

        Returns:
            tuple: (success, message, cleanup_results)
        """
        try:
            cleanup_results = []

            for mount_path in self.mounted_volumes.copy():
                unmount_result = self.unmount_dmg(mount_path)
                cleanup_results.append({
                    "mount_path": mount_path,
                    "success": unmount_result[0],
                    "message": unmount_result[1]
                })

            failed_cleanups = [r for r in cleanup_results if not r["success"]]

            if failed_cleanups:
                return (False, f"Some volumes failed to unmount: {failed_cleanups}", cleanup_results)
            else:
                return (True, "All volumes cleaned up successfully", cleanup_results)

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            return (False, f"Error during cleanup: {e}", None)

    def _get_directory_size(self, directory_path):
        """
        Get the total size of a directory in bytes

        Args:
            directory_path (str): Path to directory

        Returns:
            int: Size in bytes
        """
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(directory_path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            return total_size
        except Exception as e:
            self.logger.error(f"Error calculating directory size: {e}")
            return 0
