################################################################
#               SYSTEM OPERATIONS UTILITY                     #
#                                                              #
# AUTHOR : ZTB Automation Framework                           #
################################################################

import subprocess
import os
import platform
import psutil
import time
from shared_utils import constants
from shared_utils.common.logger import Logger


class SystemOps:
    """
    System operations utility class for ZTB automation
    
    Provides functionality for:
    - System information gathering
    - Process management
    - File system operations
    - System state verification
    """

    def __init__(self, log_handle=None):
        """
        Initialize system operations utility
        
        Args:
            log_handle: Existing logger handle, creates new if None
        """
        self.const = constants.Utils
        if log_handle is None:
            self.logger = Logger.initialize_logger("System_Ops.log", log_level="DEBUG")
        else:
            self.logger = log_handle
        
        self.logger.info("System Operations utility initialized")

    def get_system_info(self):
        """
        Get comprehensive system information
        
        Returns:
            dict: System information dictionary
        """
        try:
            system_info = {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total,
                "memory_available": psutil.virtual_memory().available,
                "disk_usage": psutil.disk_usage('/'),
                "boot_time": psutil.boot_time(),
                "current_user": os.getlogin()
            }
            
            self.logger.info("System information gathered successfully")
            return system_info
            
        except Exception as e:
            self.logger.error(f"Error gathering system information: {e}")
            return {}

    def check_disk_space(self, path="/", required_space_gb=1):
        """
        Check available disk space
        
        Args:
            path (str): Path to check disk space for
            required_space_gb (float): Required space in GB
            
        Returns:
            tuple: (sufficient_space, available_gb, required_gb)
        """
        try:
            disk_usage = psutil.disk_usage(path)
            available_gb = disk_usage.free / (1024**3)  # Convert to GB
            
            sufficient = available_gb >= required_space_gb
            
            self.logger.info(f"Disk space check - Available: {available_gb:.2f}GB, Required: {required_space_gb}GB")
            
            return (sufficient, available_gb, required_space_gb)
            
        except Exception as e:
            self.logger.error(f"Error checking disk space: {e}")
            return (False, 0, required_space_gb)

    def is_process_running(self, process_name):
        """
        Check if a process is running
        
        Args:
            process_name (str): Name of the process to check
            
        Returns:
            tuple: (is_running, process_list)
        """
        try:
            running_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if process_name.lower() in proc.info['name'].lower():
                        running_processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            is_running = len(running_processes) > 0
            
            if is_running:
                self.logger.info(f"Process '{process_name}' is running: {len(running_processes)} instances")
            else:
                self.logger.info(f"Process '{process_name}' is not running")
            
            return (is_running, running_processes)
            
        except Exception as e:
            self.logger.error(f"Error checking process: {e}")
            return (False, [])

    def kill_process_by_name(self, process_name, force=False):
        """
        Kill processes by name
        
        Args:
            process_name (str): Name of the process to kill
            force (bool): Use SIGKILL instead of SIGTERM
            
        Returns:
            tuple: (success, message, killed_processes)
        """
        try:
            killed_processes = []
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if process_name.lower() in proc.info['name'].lower():
                        pid = proc.info['pid']
                        proc_name = proc.info['name']
                        
                        if force:
                            proc.kill()  # SIGKILL
                        else:
                            proc.terminate()  # SIGTERM
                        
                        killed_processes.append({'pid': pid, 'name': proc_name})
                        self.logger.info(f"Killed process: {proc_name} (PID: {pid})")
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    self.logger.warning(f"Could not kill process: {e}")
                    continue
            
            if killed_processes:
                return (True, f"Killed {len(killed_processes)} processes", killed_processes)
            else:
                return (False, f"No processes found with name: {process_name}", [])
                
        except Exception as e:
            self.logger.error(f"Error killing processes: {e}")
            return (False, f"Error killing processes: {e}", [])

    def run_command(self, command, timeout=60, capture_output=True):
        """
        Run a system command
        
        Args:
            command (str or list): Command to run
            timeout (int): Command timeout in seconds
            capture_output (bool): Capture stdout and stderr
            
        Returns:
            tuple: (success, return_code, stdout, stderr)
        """
        try:
            self.logger.info(f"Running command: {command}")
            
            if isinstance(command, str):
                command = command.split()
            
            result = subprocess.run(
                command,
                capture_output=capture_output,
                text=True,
                timeout=timeout
            )
            
            success = result.returncode == 0
            
            if success:
                self.logger.info(f"Command completed successfully: {command[0]}")
            else:
                self.logger.error(f"Command failed with return code {result.returncode}: {command[0]}")
            
            return (success, result.returncode, result.stdout, result.stderr)
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"Command timed out: {command}")
            return (False, -1, "", "Command timed out")
        except Exception as e:
            self.logger.error(f"Error running command: {e}")
            return (False, -1, "", str(e))

    def create_directory(self, directory_path, mode=0o755):
        """
        Create directory with proper permissions
        
        Args:
            directory_path (str): Path to create
            mode (int): Directory permissions
            
        Returns:
            tuple: (success, message)
        """
        try:
            os.makedirs(directory_path, mode=mode, exist_ok=True)
            self.logger.info(f"Directory created: {directory_path}")
            return (True, f"Directory created: {directory_path}")
            
        except Exception as e:
            self.logger.error(f"Error creating directory: {e}")
            return (False, f"Error creating directory: {e}")

    def remove_directory(self, directory_path, force=False):
        """
        Remove directory and its contents
        
        Args:
            directory_path (str): Path to remove
            force (bool): Force removal even if not empty
            
        Returns:
            tuple: (success, message)
        """
        try:
            if not os.path.exists(directory_path):
                return (True, f"Directory does not exist: {directory_path}")
            
            if force:
                import shutil
                shutil.rmtree(directory_path)
            else:
                os.rmdir(directory_path)  # Only removes empty directories
            
            self.logger.info(f"Directory removed: {directory_path}")
            return (True, f"Directory removed: {directory_path}")
            
        except Exception as e:
            self.logger.error(f"Error removing directory: {e}")
            return (False, f"Error removing directory: {e}")

    def get_file_permissions(self, file_path):
        """
        Get file permissions in octal format
        
        Args:
            file_path (str): Path to file
            
        Returns:
            tuple: (success, permissions_octal, permissions_string)
        """
        try:
            if not os.path.exists(file_path):
                return (False, None, "File does not exist")
            
            stat_info = os.stat(file_path)
            permissions_octal = oct(stat_info.st_mode)[-3:]
            permissions_string = os.access(file_path, os.R_OK), os.access(file_path, os.W_OK), os.access(file_path, os.X_OK)
            
            return (True, permissions_octal, permissions_string)
            
        except Exception as e:
            self.logger.error(f"Error getting file permissions: {e}")
            return (False, None, str(e))
