#!/usr/bin/env python3
################################################################
#               UI AUTOMATION TEST FOR DMG INSTALLATION       #
#                                                              #
# AUTHOR : ZTB Automation Framework                           #
################################################################

"""
UI automation test for DMG installation using Selenium and macOS accessibility.
This test automates the installer GUI interaction.
"""

import os
import sys
import time
import subprocess
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from OS_Mac.library.agent_installation import ZTBInstaller
from shared_utils.common.logger import Logger

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    import pyautogui
    import pygetwindow as gw
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    PYAUTOGUI_AVAILABLE = False


class MacOSInstallerUIAutomation:
    """
    UI automation class for macOS installer using PyAutoGUI and system accessibility
    """
    
    def __init__(self, logger=None):
        self.logger = logger or Logger.initialize_logger("ui_automation.log", log_level="DEBUG")
        
        # Configure PyAutoGUI
        if PYAUTOGUI_AVAILABLE:
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 1  # 1 second pause between actions
        
    def find_installer_window(self, window_title_contains="Installer"):
        """
        Find the installer window using AppleScript
        """
        try:
            # Use AppleScript to find installer windows
            applescript = f'''
            tell application "System Events"
                set installerProcesses to (every process whose name contains "{window_title_contains}")
                if (count of installerProcesses) > 0 then
                    return name of first item of installerProcesses
                else
                    return "not found"
                end if
            end tell
            '''

            result = subprocess.run(
                ["osascript", "-e", applescript],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0 and "not found" not in result.stdout:
                process_name = result.stdout.strip()
                self.logger.info(f"Found installer process: {process_name}")
                return process_name
            else:
                self.logger.warning(f"No installer window found containing '{window_title_contains}'")
                return None

        except Exception as e:
            self.logger.error(f"Error finding installer window: {e}")
            return None
    
    def activate_window(self, process_name):
        """
        Activate and bring window to front using AppleScript
        """
        try:
            if process_name:
                applescript = f'''
                tell application "System Events"
                    tell process "{process_name}"
                        set frontmost to true
                    end tell
                end tell
                '''

                result = subprocess.run(
                    ["osascript", "-e", applescript],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0:
                    time.sleep(1)
                    self.logger.info(f"Activated process: {process_name}")
                    return True
                else:
                    self.logger.error(f"Failed to activate process: {result.stderr}")
                    return False
            return False
        except Exception as e:
            self.logger.error(f"Error activating window: {e}")
            return False
    
    def click_button_by_text(self, button_text, timeout=10):
        """
        Click a button by searching for text on screen
        """
        try:
            if not PYAUTOGUI_AVAILABLE:
                self.logger.error("PyAutoGUI not available")
                return False
                
            self.logger.info(f"Looking for button with text: {button_text}")
            
            # Try to find the button text on screen
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # Take a screenshot and look for the text
                    button_location = pyautogui.locateOnScreen(button_text, confidence=0.8)
                    if button_location:
                        # Click the center of the found text
                        center = pyautogui.center(button_location)
                        pyautogui.click(center)
                        self.logger.info(f"Clicked button: {button_text}")
                        return True
                except pyautogui.ImageNotFoundException:
                    pass
                
                time.sleep(1)
            
            self.logger.warning(f"Button '{button_text}' not found within {timeout} seconds")
            return False
            
        except Exception as e:
            self.logger.error(f"Error clicking button '{button_text}': {e}")
            return False
    
    def click_continue_button(self):
        """
        Click the Continue button in the installer
        """
        continue_texts = ["Continue", "Next", "Install", "Agree"]
        
        for text in continue_texts:
            if self.click_button_by_text(text):
                return True
        
        # Try clicking at common button locations
        try:
            # Common locations for Continue/Next buttons in macOS installers
            common_locations = [
                (800, 500),  # Bottom right area
                (700, 450),  # Center right
                (600, 400),  # Center
            ]
            
            for x, y in common_locations:
                pyautogui.click(x, y)
                time.sleep(2)
                self.logger.info(f"Clicked at location ({x}, {y})")
                
        except Exception as e:
            self.logger.error(f"Error clicking at common locations: {e}")
        
        return False
    
    def handle_license_agreement(self):
        """
        Handle license agreement dialog
        """
        try:
            self.logger.info("Handling license agreement...")
            
            # Look for Agree button or checkbox
            agree_texts = ["Agree", "I Agree", "Accept"]
            
            for text in agree_texts:
                if self.click_button_by_text(text):
                    time.sleep(2)
                    return True
            
            # Try pressing spacebar to check agreement checkbox
            pyautogui.press('space')
            time.sleep(1)
            
            # Then try to continue
            return self.click_continue_button()
            
        except Exception as e:
            self.logger.error(f"Error handling license agreement: {e}")
            return False
    
    def handle_installation_type(self):
        """
        Handle installation type selection
        """
        try:
            self.logger.info("Handling installation type selection...")
            
            # Usually default selection is fine, just continue
            return self.click_continue_button()
            
        except Exception as e:
            self.logger.error(f"Error handling installation type: {e}")
            return False
    
    def handle_admin_password(self, password=None):
        """
        Handle admin password prompt
        """
        try:
            self.logger.info("Handling admin password prompt...")
            
            if password:
                # Type the password
                pyautogui.typewrite(password)
                time.sleep(1)
                
                # Press Enter or click Install
                pyautogui.press('enter')
                time.sleep(2)
                
                return True
            else:
                self.logger.warning("No password provided for admin authentication")
                return False
                
        except Exception as e:
            self.logger.error(f"Error handling admin password: {e}")
            return False
    
    def wait_for_installation_complete(self, timeout=300):
        """
        Wait for installation to complete
        """
        try:
            self.logger.info(f"Waiting for installation to complete (timeout: {timeout}s)...")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                # Look for completion indicators
                completion_texts = ["Installation was successful", "Close", "Done", "Finish"]
                
                for text in completion_texts:
                    try:
                        if pyautogui.locateOnScreen(text, confidence=0.8):
                            self.logger.info(f"Found completion indicator: {text}")
                            return True
                    except pyautogui.ImageNotFoundException:
                        pass
                
                time.sleep(5)  # Check every 5 seconds
            
            self.logger.warning(f"Installation did not complete within {timeout} seconds")
            return False
            
        except Exception as e:
            self.logger.error(f"Error waiting for installation completion: {e}")
            return False
    
    def close_installer(self):
        """
        Close the installer window
        """
        try:
            self.logger.info("Closing installer...")
            
            # Try to click Close button
            close_texts = ["Close", "Done", "Finish", "Quit"]
            
            for text in close_texts:
                if self.click_button_by_text(text):
                    return True
            
            # Try pressing Cmd+Q to quit
            pyautogui.hotkey('cmd', 'q')
            time.sleep(2)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error closing installer: {e}")
            return False


def test_ui_automation_installation():
    """
    Complete UI automation test for DMG installation
    """
    print("=" * 60)
    print("UI Automation Installation Test")
    print("=" * 60)
    
    # Initialize logger
    logger = Logger.initialize_logger("ui_automation_test.log", log_level="DEBUG")
    
    if not PYAUTOGUI_AVAILABLE:
        print("❌ PyAutoGUI not available. Please install: pip install pyautogui pygetwindow")
        return
    
    try:
        # Create ZTB installer instance
        installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        ui_automation = MacOSInstallerUIAutomation(logger)
        
        # Use the DMG file
        component_dmg = "ZscalerZTBSetup-arm64-0.0.1.dmg"
        
        print(f"Testing UI automation installation of: {component_dmg}")
        print(f"Resource directory: {installer.const.ZTB_ARCHIVE_PATH}")
        
        # Mount the DMG
        dmg_path = os.path.join(installer.const.ZTB_ARCHIVE_PATH, component_dmg)
        print(f"\n🔄 Mounting DMG...")
        
        mount_result = installer.mount_dmg(dmg_path)
        if mount_result[0]:
            mount_path = mount_result[2]
            print(f"✅ DMG mounted successfully at: {mount_path}")
            
            # Find the installer package
            pkg_files = [f for f in os.listdir(mount_path) if f.endswith(('.pkg', '.mpkg'))]
            
            if pkg_files:
                pkg_file = pkg_files[0]
                pkg_path = os.path.join(mount_path, pkg_file)
                
                print(f"\n🎯 Found installer package: {pkg_file}")
                
                # Launch the installer GUI
                print(f"\n🚀 Launching installer GUI...")
                subprocess.Popen(["open", pkg_path])
                time.sleep(3)  # Wait for installer to open
                
                # Find and activate installer window
                print(f"\n🔍 Looking for installer window...")
                installer_window = ui_automation.find_installer_window("Installer")
                
                if installer_window:
                    ui_automation.activate_window(installer_window)
                    
                    print(f"\n🤖 Starting UI automation...")
                    
                    # Step 1: Handle introduction screen
                    print("   Step 1: Handling introduction screen...")
                    time.sleep(2)
                    ui_automation.click_continue_button()
                    
                    # Step 2: Handle license agreement
                    print("   Step 2: Handling license agreement...")
                    time.sleep(2)
                    ui_automation.handle_license_agreement()
                    
                    # Step 3: Handle installation type
                    print("   Step 3: Handling installation type...")
                    time.sleep(2)
                    ui_automation.handle_installation_type()
                    
                    # Step 4: Handle admin password (if needed)
                    print("   Step 4: Waiting for admin password prompt...")
                    time.sleep(3)
                    # Note: In real scenario, you might need to provide password
                    # ui_automation.handle_admin_password("your_password")
                    
                    # Step 5: Wait for installation to complete
                    print("   Step 5: Waiting for installation to complete...")
                    if ui_automation.wait_for_installation_complete():
                        print("✅ Installation completed successfully")
                        
                        # Step 6: Close installer
                        print("   Step 6: Closing installer...")
                        ui_automation.close_installer()
                        
                    else:
                        print("⏰ Installation timed out or failed")
                    
                else:
                    print("❌ Could not find installer window")
                
            else:
                print("❌ No installer packages found in DMG")
            
            # Cleanup
            print(f"\n🧹 Cleaning up...")
            time.sleep(2)  # Wait a bit before unmounting
            
            unmount_result = installer.unmount_dmg(mount_path)
            if unmount_result[0]:
                print(f"✅ DMG unmounted successfully")
            else:
                print(f"❌ Failed to unmount DMG: {unmount_result[1]}")
            
        else:
            print(f"❌ Failed to mount DMG: {mount_result[1]}")
        
        # Final cleanup
        cleanup_result = installer.cleanup_all_mounted_volumes()
        if cleanup_result[0]:
            print("✅ Final cleanup completed successfully")
        else:
            print(f"⚠️  Cleanup warning: {cleanup_result[1]}")
            
    except Exception as e:
        print(f"❌ Error during UI automation test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_ui_automation_installation()
