#!/usr/bin/env python3
################################################################
#               INDIVIDUAL DMG INSTALLATION TESTS             #
#                                                              #
# AUTHOR : ZTB Automation Framework                           #
################################################################

"""
Individual test functions for DMG installation automation.
Run specific tests by calling the respective functions.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from OS_Mac.library.agent_installation import ZTBInstaller
from shared_utils.common.logger import Logger


def test_ui_installation_only():
    """
    Test ONLY the UI-based installation (opens installer GUI)
    """
    print("=" * 60)
    print("UI-Based Installation Test (Individual)")
    print("=" * 60)
    
    # Initialize logger
    logger = Logger.initialize_logger("ui_install_individual.log", log_level="DEBUG")
    
    try:
        # Create ZTB installer instance
        installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        
        # Use the DMG file
        component_dmg = "ZscalerZTBSetup-arm64-0.0.1.dmg"
        
        print(f"Testing UI installation of: {component_dmg}")
        print(f"Resource directory: {installer.const.ZTB_ARCHIVE_PATH}")
        
        # Mount the DMG
        dmg_path = os.path.join(installer.const.ZTB_ARCHIVE_PATH, component_dmg)
        print(f"\n🔄 Mounting DMG for UI installation...")
        
        mount_result = installer.mount_dmg(dmg_path)
        if mount_result[0]:
            mount_path = mount_result[2]
            print(f"✅ DMG mounted successfully at: {mount_path}")
            
            # Find the installer package
            pkg_files = [f for f in os.listdir(mount_path) if f.endswith(('.pkg', '.mpkg'))]
            
            if pkg_files:
                pkg_file = pkg_files[0]
                pkg_path = os.path.join(mount_path, pkg_file)
                
                print(f"\n🎯 Found installer package: {pkg_file}")
                print(f"📂 Package path: {pkg_path}")
                
                # Open the installer GUI using the 'open' command
                print(f"\n🚀 Opening installer GUI...")
                print("   This will open the macOS installer interface")
                print("   The installer will guide you through the installation process")
                print("   Admin credentials will be requested when needed during installation")
                
                try:
                    import subprocess
                    # Use 'open' command to launch the installer GUI
                    result = subprocess.run(
                        ["open", pkg_path],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    
                    if result.returncode == 0:
                        print(f"✅ Installer GUI launched successfully")
                        print("   The installer window should now be open")
                        print("   Follow the on-screen instructions to complete installation")
                        
                        # Ask user if they want to wait for installation completion
                        print(f"\n⏳ Waiting for user to complete installation...")
                        input("   Press Enter after you have completed the installation (or press Ctrl+C to skip)...")
                        
                        # Now unmount the DMG
                        print(f"\n🔄 Unmounting DMG...")
                        unmount_result = installer.unmount_dmg(mount_path)
                        if unmount_result[0]:
                            print(f"✅ DMG unmounted successfully")
                        else:
                            print(f"❌ Failed to unmount DMG: {unmount_result[1]}")
                            
                    else:
                        print(f"❌ Failed to launch installer GUI: {result.stderr}")
                        
                except KeyboardInterrupt:
                    print(f"\n⏭️  Skipping wait for installation completion")
                except Exception as e:
                    print(f"❌ Error launching installer GUI: {e}")
                
            else:
                print(f"❌ No installer packages found in DMG")
            
        else:
            print(f"❌ Failed to mount DMG: {mount_result[1]}")
            
        # Cleanup
        cleanup_result = installer.cleanup_all_mounted_volumes()
        if cleanup_result[0]:
            print("✅ Cleanup completed successfully")
        else:
            print(f"⚠️  Cleanup warning: {cleanup_result[1]}")
            
    except Exception as e:
        print(f"❌ Error during UI installation test: {e}")
        import traceback
        traceback.print_exc()


def test_quiet_installation_only():
    """
    Test ONLY the quiet/silent installation
    """
    print("=" * 60)
    print("Quiet Installation Test (Individual)")
    print("=" * 60)
    
    # Initialize logger
    logger = Logger.initialize_logger("quiet_install_individual.log", log_level="DEBUG")
    
    try:
        # Create ZTB installer instance
        installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        
        # Use the DMG file
        component_dmg = "ZscalerZTBSetup-arm64-0.0.1.dmg"
        
        print(f"Testing quiet installation of: {component_dmg}")
        print(f"Resource directory: {installer.const.ZTB_ARCHIVE_PATH}")
        
        # Mount the DMG
        dmg_path = os.path.join(installer.const.ZTB_ARCHIVE_PATH, component_dmg)
        print(f"\n🔄 Mounting DMG for quiet installation...")
        
        mount_result = installer.mount_dmg(dmg_path)
        if mount_result[0]:
            mount_path = mount_result[2]
            print(f"✅ DMG mounted successfully at: {mount_path}")
            
            # Find the installer package
            pkg_files = [f for f in os.listdir(mount_path) if f.endswith(('.pkg', '.mpkg'))]
            
            if pkg_files:
                pkg_file = pkg_files[0]
                pkg_path = os.path.join(mount_path, pkg_file)
                
                print(f"\n🎯 Found installer package: {pkg_file}")
                print(f"📂 Package path: {pkg_path}")
                
                # Attempt quiet installation with sudo
                print(f"\n🤫 Attempting quiet installation...")
                print("   Note: This requires administrator privileges")
                
                try:
                    import subprocess
                    # Use installer command with sudo for quiet installation
                    install_command = [
                        "sudo", "installer", 
                        "-pkg", pkg_path, 
                        "-target", "/",
                        "-verbose"  # Use verbose for better error reporting
                    ]
                    
                    print(f"   Command: {' '.join(install_command)}")
                    print("   You may be prompted for your admin password...")
                    
                    result = subprocess.run(
                        install_command,
                        capture_output=True,
                        text=True,
                        timeout=300  # 5 minutes timeout
                    )
                    
                    if result.returncode == 0:
                        print(f"✅ Quiet installation completed successfully")
                        print(f"   Output: {result.stdout}")
                    else:
                        print(f"❌ Quiet installation failed")
                        print(f"   Return code: {result.returncode}")
                        print(f"   Error: {result.stderr}")
                        print(f"   Output: {result.stdout}")
                        
                except subprocess.TimeoutExpired:
                    print(f"⏰ Installation timed out (may still be running in background)")
                except Exception as e:
                    print(f"❌ Error during quiet installation: {e}")
                
            else:
                print(f"❌ No installer packages found in DMG")
            
            # Unmount the DMG
            print(f"\n🔄 Unmounting DMG...")
            unmount_result = installer.unmount_dmg(mount_path)
            if unmount_result[0]:
                print(f"✅ DMG unmounted successfully")
            else:
                print(f"❌ Failed to unmount DMG: {unmount_result[1]}")
            
        else:
            print(f"❌ Failed to mount DMG: {mount_result[1]}")
            
        # Cleanup
        cleanup_result = installer.cleanup_all_mounted_volumes()
        if cleanup_result[0]:
            print("✅ Cleanup completed successfully")
        else:
            print(f"⚠️  Cleanup warning: {cleanup_result[1]}")
            
    except Exception as e:
        print(f"❌ Error during quiet installation test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "ui":
            test_ui_installation_only()
        elif test_type == "quiet":
            test_quiet_installation_only()
        else:
            print("Usage: python3 individual_tests.py [ui|quiet]")
            print("  ui    - Run UI-based installation test")
            print("  quiet - Run quiet installation test")
    else:
        print("Available individual tests:")
        print("  python3 individual_tests.py ui    - UI-based installation")
        print("  python3 individual_tests.py quiet - Quiet installation")
