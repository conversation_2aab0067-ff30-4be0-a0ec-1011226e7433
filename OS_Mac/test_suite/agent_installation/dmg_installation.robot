*** Settings ***
Documentation    DMG Installation Test Suite using Robot Framework
Library          Process
Library          OperatingSystem
Library          Collections
Library          String
Library          ../../../shared_utils/robot_keywords/DMGInstallationKeywords.py

Suite Setup      Setup Test Environment
Suite Teardown   Cleanup Test Environment

*** Variables ***
${DMG_FILE}              ZscalerZTBSetup-arm64-0.0.1.dmg
${RESOURCE_DIR}          ${CURDIR}/../../../OS_Mac/resource
${LOG_DIR}               ${CURDIR}/../../../Reports/logs
${INSTALLER_TIMEOUT}     300
${UI_TIMEOUT}           30

*** Test Cases ***
Test DMG Mounting and Detection
    [Documentation]    Test basic DMG mounting and package detection
    [Tags]             smoke    mounting
    
    Log    Starting DMG mounting test
    ${result}=    Mount DMG File    ${DMG_FILE}
    Should Be True    ${result}[0]    Failed to mount DMG: ${result}[1]
    
    ${mount_path}=    Set Variable    ${result}[2]
    Log    DMG mounted at: ${mount_path}
    
    ${contents}=    List Directory Contents    ${mount_path}
    Log    DMG contents: ${contents}
    
    ${pkg_files}=    Find Package Files    ${mount_path}
    Should Not Be Empty    ${pkg_files}    No installer packages found
    Log    Found packages: ${pkg_files}
    
    ${unmount_result}=    Unmount DMG    ${mount_path}
    Should Be True    ${unmount_result}[0]    Failed to unmount DMG

Test UI Installation Launch
    [Documentation]    Test launching the installer GUI
    [Tags]             ui    installation
    
    Log    Starting UI installation test
    ${result}=    Mount DMG File    ${DMG_FILE}
    Should Be True    ${result}[0]    Failed to mount DMG: ${result}[1]
    
    ${mount_path}=    Set Variable    ${result}[2]
    ${pkg_files}=    Find Package Files    ${mount_path}
    ${pkg_path}=    Set Variable    ${mount_path}/${pkg_files}[0]
    
    Log    Launching installer GUI for: ${pkg_path}
    ${launch_result}=    Launch Installer GUI    ${pkg_path}
    Should Be True    ${launch_result}    Failed to launch installer GUI
    
    Sleep    5s    Wait for installer to fully load
    
    ${window_found}=    Find Installer Window
    Should Be True    ${window_found}    Installer window not found
    
    Log    Installer GUI launched successfully
    # Note: In real test, you would continue with UI automation steps
    
    # Cleanup - close installer if still open
    Close Installer Window
    ${unmount_result}=    Unmount DMG    ${mount_path}
    Should Be True    ${unmount_result}[0]    Failed to unmount DMG

Test Quiet Installation
    [Documentation]    Test quiet/silent installation with sudo
    [Tags]             quiet    installation    sudo
    
    Log    Starting quiet installation test
    ${result}=    Mount DMG File    ${DMG_FILE}
    Should Be True    ${result}[0]    Failed to mount DMG: ${result}[1]
    
    ${mount_path}=    Set Variable    ${result}[2]
    ${pkg_files}=    Find Package Files    ${mount_path}
    ${pkg_path}=    Set Variable    ${mount_path}/${pkg_files}[0]
    
    Log    Attempting quiet installation: ${pkg_path}
    # Note: This will require admin password
    ${install_result}=    Run Quiet Installation    ${pkg_path}
    
    # Check if installation succeeded or failed due to permissions
    Run Keyword If    ${install_result}[0]
    ...    Log    Installation completed successfully
    ...    ELSE
    ...    Log    Installation failed (likely due to admin permissions): ${install_result}[1]
    
    ${unmount_result}=    Unmount DMG    ${mount_path}
    Should Be True    ${unmount_result}[0]    Failed to unmount DMG

Test Complete UI Automation Workflow
    [Documentation]    Complete UI automation workflow for installation
    [Tags]             ui    automation    workflow
    
    Log    Starting complete UI automation workflow
    ${result}=    Mount DMG File    ${DMG_FILE}
    Should Be True    ${result}[0]    Failed to mount DMG: ${result}[1]
    
    ${mount_path}=    Set Variable    ${result}[2]
    ${pkg_files}=    Find Package Files    ${mount_path}
    ${pkg_path}=    Set Variable    ${mount_path}/${pkg_files}[0]
    
    # Launch installer
    ${launch_result}=    Launch Installer GUI    ${pkg_path}
    Should Be True    ${launch_result}    Failed to launch installer GUI
    
    Sleep    3s    Wait for installer to load
    
    # Find and activate installer window
    ${window_found}=    Find Installer Window
    Should Be True    ${window_found}    Installer window not found
    
    Activate Installer Window
    
    # Automate installation steps
    Log    Step 1: Handle introduction screen
    Handle Introduction Screen
    
    Log    Step 2: Handle license agreement
    Handle License Agreement
    
    Log    Step 3: Handle installation type
    Handle Installation Type Selection
    
    Log    Step 4: Handle destination selection
    Handle Destination Selection
    
    Log    Step 5: Start installation
    Start Installation Process
    
    # Note: Admin password handling would be done here in real scenario
    Log    Note: Admin password prompt may appear - handle manually or with credentials
    
    # Wait for completion (with timeout)
    ${completion_result}=    Wait For Installation Completion    ${INSTALLER_TIMEOUT}
    
    Run Keyword If    ${completion_result}
    ...    Log    Installation completed successfully
    ...    ELSE
    ...    Log    Installation timed out or failed
    
    # Close installer
    Close Installer Window
    
    # Cleanup
    ${unmount_result}=    Unmount DMG    ${mount_path}
    Should Be True    ${unmount_result}[0]    Failed to unmount DMG

Test Installation Verification
    [Documentation]    Verify that the application was installed correctly
    [Tags]             verification    post-install
    
    Log    Verifying installation
    
    # Check common installation locations
    ${app_locations}=    Create List
    ...    /Applications/Zscaler
    ...    /Applications/Zscaler Zero Trust Browser
    ...    /usr/local/bin/zscaler
    
    ${found_installation}=    Set Variable    ${False}
    
    FOR    ${location}    IN    @{app_locations}
        ${exists}=    Run Keyword And Return Status    Directory Should Exist    ${location}
        Run Keyword If    ${exists}
        ...    Run Keywords
        ...    Log    Found installation at: ${location}
        ...    AND    Set Variable    ${found_installation}    ${True}
        ...    AND    Exit For Loop
    END
    
    # Check for installed packages using pkgutil
    ${pkg_result}=    Run Process    pkgutil    --packages    shell=True
    ${pkg_output}=    Set Variable    ${pkg_result.stdout}
    
    ${zscaler_packages}=    Get Lines Containing String    ${pkg_output}    zscaler    case_insensitive=True
    
    Run Keyword If    '${zscaler_packages}' != ''
    ...    Log    Found Zscaler packages: ${zscaler_packages}
    
    # Log verification results
    Run Keyword If    ${found_installation}
    ...    Log    ✅ Installation verification successful
    ...    ELSE
    ...    Log    ⚠️ Installation verification inconclusive - may require manual check

*** Keywords ***
Setup Test Environment
    [Documentation]    Setup test environment and dependencies
    Log    Setting up test environment
    
    # Check if required directories exist
    Directory Should Exist    ${RESOURCE_DIR}    Resource directory not found
    Create Directory    ${LOG_DIR}
    
    # Check if DMG file exists
    File Should Exist    ${RESOURCE_DIR}/${DMG_FILE}    DMG file not found
    
    # Check for required Python packages
    ${pyautogui_check}=    Run Process    python3    -c    import pyautogui    shell=True
    Run Keyword If    ${pyautogui_check.rc} != 0
    ...    Log    Warning: PyAutoGUI not available - UI automation may be limited
    
    Log    Test environment setup complete

Cleanup Test Environment
    [Documentation]    Cleanup test environment
    Log    Cleaning up test environment
    
    # Cleanup any remaining mounted volumes
    Cleanup All Mounted Volumes
    
    # Close any remaining installer windows
    Close All Installer Windows
    
    Log    Test environment cleanup complete

Handle Introduction Screen
    [Documentation]    Handle the installer introduction screen
    Sleep    2s
    Click Continue Button

Handle License Agreement
    [Documentation]    Handle license agreement screen
    Sleep    2s
    Accept License Agreement
    Click Continue Button

Handle Installation Type Selection
    [Documentation]    Handle installation type selection
    Sleep    2s
    # Usually default selection is fine
    Click Continue Button

Handle Destination Selection
    [Documentation]    Handle destination selection
    Sleep    2s
    # Usually default destination is fine
    Click Continue Button

Start Installation Process
    [Documentation]    Start the actual installation process
    Sleep    2s
    Click Install Button
