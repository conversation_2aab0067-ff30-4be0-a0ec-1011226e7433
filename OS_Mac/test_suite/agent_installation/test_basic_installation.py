################################################################
#               Agent INSTALLATION TESTS                        #
#                                                              #
# AUTHOR : Agent Automation Framework                           #
################################################################

import pytest
import os
import tempfile
from unittest.mock import patch, MagicMock


class TestBasicAgentInstallation:
    """
    Test cases for basic Agent installation functionality
    """

    @pytest.mark.smoke
    @pytest.mark.installation
    def test_dmg_installer_initialization(self, logger):
        """
        Test Agent installer initialization
        """
        from OS_Mac.library.agent_installation import ZTBInstaller
        
        installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        
        assert installer is not None
        assert installer.logger is not None
        assert installer.dmg_archive_path is not None
        assert isinstance(installer.mounted_volumes, list)
        assert isinstance(installer.installed_applications, list)

    @pytest.mark.installation
    def test_list_available_dmg_files_empty_directory(self, dmg_installer, temp_directory):
        """
        Test listing Agent files when directory is empty
        """
        # Mock the dmg_archive_path to point to empty temp directory
        dmg_installer.dmg_archive_path = temp_directory
        
        result = dmg_installer.list_available_dmg_files()
        
        assert result[0] is False  # Should fail
        assert "No Agent files found" in result[1]
        assert result[2] is None

    @pytest.mark.installation
    def test_list_available_dmg_files_with_dmg_files(self, dmg_installer, temp_directory):
        """
        Test listing Agent files when Agent files are present
        """
        # Create mock Agent files
        dmg_files = ["test1.dmg", "test2.dmg", "not_dmg.txt"]
        for file_name in dmg_files:
            file_path = os.path.join(temp_directory, file_name)
            with open(file_path, 'w') as f:
                f.write("mock content")
        
        # Mock the dmg_archive_path
        dmg_installer.dmg_archive_path = temp_directory
        
        result = dmg_installer.list_available_dmg_files()
        
        assert result[0] is True  # Should succeed
        assert "Agent files found" in result[1]
        assert len(result[2]) == 2  # Only .dmg files should be listed
        assert "test1.dmg" in result[2]
        assert "test2.dmg" in result[2]
        assert "not_dmg.txt" not in result[2]

    @pytest.mark.installation
    def test_verify_dmg_files_exist_missing_directory(self, dmg_installer):
        """
        Test Agent file verification when archive directory doesn't exist
        """
        # Set non-existent path
        dmg_installer.dmg_archive_path = "/non/existent/path"
        
        result = dmg_installer.verify_dmg_files_exist()
        
        assert result[0] is False
        assert "archive path does not exist" in result[1]

    @pytest.mark.installation
    @patch('subprocess.run')
    def test_mount_dmg_success(self, mock_subprocess, dmg_installer, temp_directory):
        """
        Test successful Agent mounting
        """
        # Create a mock Agent file
        dmg_path = os.path.join(temp_directory, "test.dmg")
        with open(dmg_path, 'w') as f:
            f.write("mock dmg content")
        
        # Mock subprocess.run to simulate successful hdiutil attach
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "/dev/disk2s1    Apple_HFS    /Volumes/TestApp"
        mock_subprocess.return_value = mock_result
        
        result = dmg_installer.mount_dmg(dmg_path)
        
        assert result[0] is True
        assert "Agent mounted successfully" in result[1]
        assert result[2] == "/Volumes/TestApp"
        assert "/Volumes/TestApp" in dmg_installer.mounted_volumes

    @pytest.mark.installation
    @patch('subprocess.run')
    def test_mount_dmg_failure(self, mock_subprocess, dmg_installer, temp_directory):
        """
        Test Agent mounting failure
        """
        # Create a mock Agent file
        dmg_path = os.path.join(temp_directory, "test.dmg")
        with open(dmg_path, 'w') as f:
            f.write("mock dmg content")
        
        # Mock subprocess.run to simulate failed hdiutil attach
        mock_result = MagicMock()
        mock_result.returncode = 1
        mock_result.stderr = "hdiutil: attach failed - image not recognized"
        mock_subprocess.return_value = mock_result
        
        result = dmg_installer.mount_dmg(dmg_path)
        
        assert result[0] is False
        assert "Failed to mount Agent" in result[1]
        assert result[2] is None

    @pytest.mark.installation
    def test_mount_dmg_file_not_found(self, dmg_installer):
        """
        Test Agent mounting when file doesn't exist
        """
        non_existent_path = "/path/to/non/existent/file.dmg"
        
        result = dmg_installer.mount_dmg(non_existent_path)
        
        assert result[0] is False
        assert "Agent file not found" in result[1]
        assert result[2] is None

    @pytest.mark.installation
    def test_extract_mount_path_valid_output(self, dmg_installer):
        """
        Test mount path extraction from hdiutil output
        """
        hdiutil_output = "/dev/disk2s1    Apple_HFS    /Volumes/TestApp\n"
        
        mount_path = dmg_installer._extract_mount_path(hdiutil_output)
        
        assert mount_path == "/Volumes/TestApp"

    @pytest.mark.installation
    def test_extract_mount_path_invalid_output(self, dmg_installer):
        """
        Test mount path extraction from invalid hdiutil output
        """
        hdiutil_output = "Invalid output without mount path"
        
        mount_path = dmg_installer._extract_mount_path(hdiutil_output)
        
        assert mount_path is None

    @pytest.mark.installation
    def test_find_app_in_dmg_success(self, dmg_installer, temp_directory):
        """
        Test finding applications in mounted Agent
        """
        # Create mock app bundle
        app_path = os.path.join(temp_directory, "TestApp.app")
        os.makedirs(app_path)
        
        # Create another non-app file
        other_file = os.path.join(temp_directory, "README.txt")
        with open(other_file, 'w') as f:
            f.write("readme content")
        
        result = dmg_installer.find_app_in_dmg(temp_directory)
        
        assert result[0] is True
        assert "Found 1 applications" in result[1]
        assert len(result[2]) == 1
        assert app_path in result[2]

    @pytest.mark.installation
    def test_find_app_in_dmg_no_apps(self, dmg_installer, temp_directory):
        """
        Test finding applications when no apps are present
        """
        # Create non-app files
        files = ["README.txt", "installer.pkg", "license.pdf"]
        for file_name in files:
            file_path = os.path.join(temp_directory, file_name)
            with open(file_path, 'w') as f:
                f.write("content")
        
        result = dmg_installer.find_app_in_dmg(temp_directory)
        
        assert result[0] is False
        assert "No .app bundles found" in result[1]
        assert result[2] is None

    @pytest.mark.installation
    def test_find_app_in_dmg_invalid_path(self, dmg_installer):
        """
        Test finding applications with invalid mount path
        """
        invalid_path = "/non/existent/path"
        
        result = dmg_installer.find_app_in_dmg(invalid_path)
        
        assert result[0] is False
        assert "Mount path does not exist" in result[1]
        assert result[2] is None

    @pytest.mark.installation
    @patch('shutil.copytree')
    def test_copy_app_to_applications_success(self, mock_copytree, dmg_installer, sample_app_bundle, temp_directory):
        """
        Test successful application copying
        """
        # Mock Applications directory
        mock_apps_dir = os.path.join(temp_directory, "Applications")
        os.makedirs(mock_apps_dir, exist_ok=True)
        dmg_installer.const.APPLICATIONS_PATH = mock_apps_dir
        
        result = dmg_installer.copy_app_to_applications(sample_app_bundle)
        
        assert result[0] is True
        assert "Application copied successfully" in result[1]
        assert result[2] is not None
        mock_copytree.assert_called_once()

    @pytest.mark.installation
    def test_copy_app_existing_app_no_overwrite(self, dmg_installer, sample_app_bundle, temp_directory):
        """
        Test copying application when it already exists and overwrite is disabled
        """
        # Mock Applications directory with existing app
        mock_apps_dir = os.path.join(temp_directory, "Applications")
        os.makedirs(mock_apps_dir, exist_ok=True)
        existing_app = os.path.join(mock_apps_dir, "TestApp.app")
        os.makedirs(existing_app, exist_ok=True)
        
        dmg_installer.const.APPLICATIONS_PATH = mock_apps_dir
        
        result = dmg_installer.copy_app_to_applications(sample_app_bundle, force_overwrite=False)
        
        assert result[0] is False
        assert "Application already exists" in result[1]
        assert result[2] is None
