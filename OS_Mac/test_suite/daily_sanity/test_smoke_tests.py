################################################################
#               SMOKE TESTS FOR ZTB AUTOMATION                #
#                                                              #
# AUTHOR : ZTB Automation Framework                           #
################################################################

import pytest
import os
from unittest.mock import patch, MagicMock


class TestSmokeTests:
    """
    Quick smoke tests to verify basic functionality
    """

    @pytest.mark.smoke
    def test_framework_imports(self):
        """
        Test that all framework modules can be imported
        """
        try:
            from OS_Mac.library.agent_installation import ZTBInstaller
            from OS_Mac.library.system_ops import SystemOps
            from shared_utils.common.logger import Logger
            from shared_utils.helpers import Helpers
            from shared_utils import constants
            
            # If we get here, all imports succeeded
            assert True
            
        except ImportError as e:
            pytest.fail(f"Failed to import framework modules: {e}")

    @pytest.mark.smoke
    def test_logger_initialization(self):
        """
        Test that logger can be initialized
        """
        from shared_utils.common.logger import Logger
        
        logger = Logger.initialize_logger("smoke_test.log", log_level="INFO")
        
        assert logger is not None
        assert logger.name == "smoke_test.log"

    @pytest.mark.smoke
    def test_constants_access(self):
        """
        Test that constants can be accessed
        """
        from shared_utils.common import constants
        
        utils = constants.Utils()
        
        assert hasattr(utils, 'ZTB_ARCHIVE_PATH')
        assert hasattr(utils, 'APPLICATIONS_PATH')
        assert hasattr(utils, 'MOUNT_TIMEOUT')

    @pytest.mark.smoke
    def test_ztb_installer_basic_initialization(self, logger):
        """
        Test basic ZTB installer initialization
        """
        from OS_Mac.library.agent_installation import ZTBInstaller
        
        installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        
        assert installer is not None
        assert installer.logger is not None
        assert hasattr(installer, 'mount_dmg')
        assert hasattr(installer, 'install_component')
        assert hasattr(installer, 'verify_installation')

    @pytest.mark.smoke
    def test_system_ops_basic_initialization(self, logger):
        """
        Test basic system operations initialization
        """
        from OS_Mac.library.system_ops import SystemOps
        
        system_ops = SystemOps(log_handle=logger)
        
        assert system_ops is not None
        assert system_ops.logger is not None
        assert hasattr(system_ops, 'get_system_info')
        assert hasattr(system_ops, 'run_command')

    @pytest.mark.smoke
    def test_helper_functions_basic_methods(self):
        """
        Test basic helper function methods
        """
        from shared_utils.helpers import Helpers
        
        # Test file size formatting
        size_str = Helpers.format_file_size(1024)
        assert "KB" in size_str
        
        # Test timestamp generation
        timestamp = Helpers.get_timestamp()
        assert isinstance(timestamp, str)
        assert len(timestamp) > 0

    @pytest.mark.smoke
    def test_directory_structure_exists(self):
        """
        Test that required directory structure exists
        """
        required_dirs = [
            "OS_Mac",
            "OS_Mac/library",
            "OS_Mac/resource",
            "OS_Mac/test_suite",
            "shared_utils",
            "config",
            "Reports"
        ]
        
        for dir_path in required_dirs:
            assert os.path.exists(dir_path), f"Required directory missing: {dir_path}"

    @pytest.mark.smoke
    def test_required_files_exist(self):
        """
        Test that required files exist
        """
        required_files = [
            "requirements.txt",
            "pytest.ini",
            "shared_utils/constants.py",
            "shared_utils/logger.py",
            "OS_Mac/library/ztb_installation.py",
            "OS_Mac/library/system_ops.py"
        ]
        
        for file_path in required_files:
            assert os.path.exists(file_path), f"Required file missing: {file_path}"

    @pytest.mark.smoke
    @patch('subprocess.run')
    def test_basic_command_execution(self, mock_subprocess, system_ops):
        """
        Test basic command execution capability
        """
        # Mock successful command execution
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "test output"
        mock_result.stderr = ""
        mock_subprocess.return_value = mock_result
        
        result = system_ops.run_command(["echo", "test"])
        
        assert result[0] is True
        assert result[1] == 0
        assert "test output" in result[2]

    @pytest.mark.smoke
    def test_temp_directory_operations(self):
        """
        Test temporary directory operations
        """
        from shared_utils.helpers import Helpers
        
        # Create temp directory
        temp_dir = Helpers.create_temp_directory()
        assert temp_dir is not None
        assert os.path.exists(temp_dir)
        
        # Cleanup temp directory
        cleanup_result = Helpers.cleanup_temp_directory(temp_dir)
        assert cleanup_result is True
        assert not os.path.exists(temp_dir)

    @pytest.mark.smoke
    def test_dmg_file_validation_basic(self):
        """
        Test basic Agent file validation
        """
        from shared_utils.helpers import Helpers
        import tempfile
        
        # Test with non-existent file
        result = Helpers.validate_dmg_file("/non/existent/file.dmg")
        assert result[0] is False
        assert "does not exist" in result[1]
        
        # Test with non-Agent file
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as temp_file:
            temp_file.write(b"test content")
            temp_file_path = temp_file.name
        
        try:
            result = Helpers.validate_dmg_file(temp_file_path)
            assert result[0] is False
            assert "does not have .dmg extension" in result[1]
        finally:
            os.unlink(temp_file_path)

    @pytest.mark.smoke
    def test_pytest_markers_configured(self):
        """
        Test that pytest.ini is present and contains marker definitions.
        """
        pytest_ini_path = os.path.join(os.getcwd(), 'pytest.ini')
        assert os.path.exists(pytest_ini_path), "pytest.ini file not found"

        with open(pytest_ini_path, 'r') as f:
            content = f.read()

        assert '[pytest]' in content
        assert 'markers =' in content
        assert 'smoke:' in content
        assert 'installation:' in content


class TestIntegrationSmoke:
    """
    Basic integration smoke tests
    """

    @pytest.mark.smoke
    @patch('subprocess.run')
    def test_end_to_end_workflow_mock(self, mock_subprocess, ztb_installer, temp_directory):
        """
        Test end-to-end workflow with mocked external calls
        """
        # Create mock Agent file
        dmg_file = "test_app.dmg"
        dmg_path = os.path.join(temp_directory, dmg_file)
        with open(dmg_path, 'w') as f:
            f.write("mock dmg content")
        
        # Mock the dmg_archive_path
        ztb_installer.ztb_archive_path = temp_directory
        
        # Mock successful hdiutil operations
        def mock_run_side_effect(*args, **kwargs):
            mock_result = MagicMock()
            if "attach" in args[0]:
                mock_result.returncode = 0
                mock_result.stdout = "/dev/disk2s1    Apple_HFS    /Volumes/TestApp"
            elif "detach" in args[0]:
                mock_result.returncode = 0
                mock_result.stdout = ""
            else:
                mock_result.returncode = 0
                mock_result.stdout = ""
            return mock_result
        
        mock_subprocess.side_effect = mock_run_side_effect
        
        # Create mock app in mount directory
        mount_dir = os.path.join(temp_directory, "mount")
        os.makedirs(mount_dir, exist_ok=True)
        app_path = os.path.join(mount_dir, "TestApp.app")
        os.makedirs(app_path, exist_ok=True)
        
        # Mock find_app_in_dmg to return our mock app
        original_find_app = ztb_installer.find_app_in_dmg
        ztb_installer.find_app_in_dmg = lambda mount_path: (True, "Found 1 applications", [app_path])
        
        # Mock copy operation
        original_copy_app = ztb_installer.copy_app_to_applications
        ztb_installer.copy_app_to_applications = lambda app_path, force=False: (True, "App copied", "/Applications/TestApp.app")
        
        try:
            # Test the workflow
            result = ztb_installer.install_component(dmg_file, installation_mode="copy_app")
            
            assert result[0] is True
            assert "installation completed successfully" in result[1].lower()
            assert result[2] is not None
            
        finally:
            # Restore original methods
            ztb_installer.find_app_in_dmg = original_find_app
            ztb_installer.copy_app_to_applications = original_copy_app

    @pytest.mark.smoke
    def test_error_handling_smoke(self, ztb_installer):
        """
        Test basic error handling
        """
        # Test with non-existent Agent file
        result = ztb_installer.install_component("non_existent.dmg")
        
        assert result[0] is False
        assert "not found" in result[1]
        assert result[2] is None
