################################################################
#               Agent INSTALLATION VERIFICATION TESTS          #
#                                                              #
# AUTHOR : Agent Automation Framework                           #
################################################################

import pytest
import os
import tempfile
from unittest.mock import patch, MagicMock


class TestInstallationVerification:
    """
    Test cases for verifying Agent installation results
    """

    @pytest.mark.verification
    def test_verify_installation_success(self, dmg_installer, sample_app_bundle, temp_directory):
        """
        Test successful installation verification
        """
        # Mock Applications directory with the app
        mock_apps_dir = os.path.join(temp_directory, "Applications")
        os.makedirs(mock_apps_dir, exist_ok=True)
        
        # Copy sample app to mock Applications directory
        import shutil
        app_name = "TestApp.app"
        dest_app_path = os.path.join(mock_apps_dir, app_name)
        shutil.copytree(sample_app_bundle, dest_app_path)
        
        # Mock the Applications path
        dmg_installer.const.APPLICATIONS_PATH = mock_apps_dir
        
        result = dmg_installer.verify_installation(app_name)
        
        assert result[0] is True
        assert "Application verification successful" in result[1]
        assert result[2] is not None
        assert result[2]["app_name"] == app_name
        assert result[2]["app_path"] == dest_app_path

    @pytest.mark.verification
    def test_verify_installation_app_not_found(self, dmg_installer, temp_directory):
        """
        Test verification when application is not found
        """
        # Mock empty Applications directory
        mock_apps_dir = os.path.join(temp_directory, "Applications")
        os.makedirs(mock_apps_dir, exist_ok=True)
        dmg_installer.const.APPLICATIONS_PATH = mock_apps_dir
        
        app_name = "NonExistentApp.app"
        result = dmg_installer.verify_installation(app_name)
        
        assert result[0] is False
        assert "Application not found" in result[1]
        assert result[2] is None

    @pytest.mark.verification
    def test_verify_installation_invalid_app_bundle(self, dmg_installer, temp_directory):
        """
        Test verification with invalid app bundle (missing Info.plist)
        """
        # Mock Applications directory
        mock_apps_dir = os.path.join(temp_directory, "Applications")
        os.makedirs(mock_apps_dir, exist_ok=True)
        
        # Create invalid app bundle (missing Info.plist)
        app_name = "InvalidApp.app"
        invalid_app_path = os.path.join(mock_apps_dir, app_name)
        os.makedirs(invalid_app_path, exist_ok=True)
        
        dmg_installer.const.APPLICATIONS_PATH = mock_apps_dir
        
        result = dmg_installer.verify_installation(app_name)
        
        assert result[0] is False
        assert "Invalid app bundle - missing Info.plist" in result[1]
        assert result[2] is None

    @pytest.mark.verification
    def test_verify_installation_not_app_bundle(self, dmg_installer, temp_directory):
        """
        Test verification with file that's not an app bundle
        """
        # Mock Applications directory
        mock_apps_dir = os.path.join(temp_directory, "Applications")
        os.makedirs(mock_apps_dir, exist_ok=True)
        
        # Create regular file instead of app bundle
        file_name = "NotAnApp.txt"
        file_path = os.path.join(mock_apps_dir, file_name)
        with open(file_path, 'w') as f:
            f.write("This is not an app")
        
        dmg_installer.const.APPLICATIONS_PATH = mock_apps_dir
        
        result = dmg_installer.verify_installation(file_name)
        
        assert result[0] is False
        assert "Not a valid app bundle" in result[1]
        assert result[2] is None

    @pytest.mark.verification
    def test_get_directory_size(self, dmg_installer, sample_app_bundle):
        """
        Test directory size calculation
        """
        size = dmg_installer._get_directory_size(sample_app_bundle)
        
        assert isinstance(size, int)
        assert size > 0  # Should have some size due to created files

    @pytest.mark.verification
    def test_get_directory_size_nonexistent(self, dmg_installer):
        """
        Test directory size calculation for non-existent directory
        """
        size = dmg_installer._get_directory_size("/non/existent/path")
        
        assert size == 0

    @pytest.mark.verification
    @patch('subprocess.run')
    def test_cleanup_all_mounted_volumes_success(self, mock_subprocess, dmg_installer):
        """
        Test successful cleanup of all mounted volumes
        """
        # Add some mock mounted volumes
        dmg_installer.mounted_volumes = ["/Volumes/TestApp1", "/Volumes/TestApp2"]
        
        # Mock successful unmount
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_subprocess.return_value = mock_result
        
        result = dmg_installer.cleanup_all_mounted_volumes()
        
        assert result[0] is True
        assert "All volumes cleaned up successfully" in result[1]
        assert len(result[2]) == 2
        assert all(r["success"] for r in result[2])
        assert len(dmg_installer.mounted_volumes) == 0

    @pytest.mark.verification
    @patch('subprocess.run')
    def test_cleanup_all_mounted_volumes_partial_failure(self, mock_subprocess, dmg_installer):
        """
        Test cleanup with some volumes failing to unmount
        """
        # Add some mock mounted volumes
        dmg_installer.mounted_volumes = ["/Volumes/TestApp1", "/Volumes/TestApp2"]
        
        # Mock mixed success/failure
        def mock_run_side_effect(*args, **kwargs):
            mock_result = MagicMock()
            if "/Volumes/TestApp1" in args[0]:
                mock_result.returncode = 0  # Success
            else:
                mock_result.returncode = 1  # Failure
                mock_result.stderr = "Device busy"
            return mock_result
        
        mock_subprocess.side_effect = mock_run_side_effect
        
        result = dmg_installer.cleanup_all_mounted_volumes()
        
        assert result[0] is False
        assert "Some volumes failed to unmount" in result[1]
        assert len(result[2]) == 2
        assert result[2][0]["success"] is True
        assert result[2][1]["success"] is False

    @pytest.mark.verification
    def test_cleanup_all_mounted_volumes_empty_list(self, dmg_installer):
        """
        Test cleanup when no volumes are mounted
        """
        # Ensure no mounted volumes
        dmg_installer.mounted_volumes = []
        
        result = dmg_installer.cleanup_all_mounted_volumes()
        
        assert result[0] is True
        assert "All volumes cleaned up successfully" in result[1]
        assert len(result[2]) == 0


class TestSystemVerification:
    """
    Test cases for system-level verification
    """

    @pytest.mark.verification
    def test_system_info_gathering(self, system_ops):
        """
        Test system information gathering
        """
        system_info = system_ops.get_system_info()
        
        assert isinstance(system_info, dict)
        assert "platform" in system_info
        assert "system" in system_info
        assert "cpu_count" in system_info
        assert "memory_total" in system_info

    @pytest.mark.verification
    def test_disk_space_check_sufficient(self, system_ops):
        """
        Test disk space check with sufficient space
        """
        # Check for very small requirement (should pass)
        result = system_ops.check_disk_space("/", required_space_gb=0.001)
        
        assert result[0] is True  # Should have sufficient space
        assert result[1] > 0  # Available space should be positive
        assert result[2] == 0.001  # Required space should match

    @pytest.mark.verification
    def test_disk_space_check_insufficient(self, system_ops):
        """
        Test disk space check with insufficient space
        """
        # Check for unreasonably large requirement (should fail)
        result = system_ops.check_disk_space("/", required_space_gb=999999)
        
        assert result[0] is False  # Should not have sufficient space
        assert result[1] > 0  # Available space should still be positive
        assert result[2] == 999999  # Required space should match

    @pytest.mark.verification
    def test_process_check_nonexistent(self, system_ops):
        """
        Test checking for non-existent process
        """
        result = system_ops.is_process_running("NonExistentProcess12345")
        
        assert result[0] is False
        assert len(result[1]) == 0

    @pytest.mark.verification
    def test_run_command_success(self, system_ops):
        """
        Test running successful command
        """
        result = system_ops.run_command(["echo", "test"])
        
        assert result[0] is True
        assert result[1] == 0  # Return code should be 0
        assert "test" in result[2]  # stdout should contain "test"

    @pytest.mark.verification
    def test_run_command_failure(self, system_ops):
        """
        Test running failing command
        """
        result = system_ops.run_command(["false"])  # Command that always fails
        
        assert result[0] is False
        assert result[1] != 0  # Return code should be non-zero

    @pytest.mark.verification
    def test_create_and_remove_directory(self, system_ops, temp_directory):
        """
        Test directory creation and removal
        """
        test_dir = os.path.join(temp_directory, "test_directory")
        
        # Create directory
        create_result = system_ops.create_directory(test_dir)
        assert create_result[0] is True
        assert os.path.exists(test_dir)
        
        # Remove directory
        remove_result = system_ops.remove_directory(test_dir)
        assert remove_result[0] is True
        assert not os.path.exists(test_dir)
