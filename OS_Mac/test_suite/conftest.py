################################################################
#               PYTEST CONFIGURATION                          #
#                                                              #
# AUTHOR : Agent Automation Framework                           #
################################################################

import pytest
import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from OS_Mac.library.agent_installation import ZTBInstaller
from OS_Mac.library.system_ops import SystemOps
from shared_utils.common.logger import Logger
from shared_utils.helpers import Helpers

@pytest.fixture(scope="session")
def logger():
    """
    Session-scoped logger fixture
    """
    return Logger.initialize_logger("test_session.log", log_level="DEBUG")


@pytest.fixture(scope="session")
def system_ops(logger):
    """
    Session-scoped system operations fixture
    """
    return SystemOps(log_handle=logger)


@pytest.fixture(scope="function")
def ztb_installer(logger):
    """
    Function-scoped ZTB installer fixture
    """
    installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
    yield installer
    # Cleanup any mounted volumes after each test
    installer.cleanup_all_mounted_volumes()


@pytest.fixture(scope="function")
def temp_directory():
    """
    Function-scoped temporary directory fixture
    """
    temp_dir = Helpers.create_temp_directory()
    yield temp_dir
    # Cleanup temporary directory after test
    Helpers.cleanup_temp_directory(temp_dir)


@pytest.fixture(scope="session")
def test_data_dir():
    """
    Session-scoped test data directory fixture
    """
    test_data_path = Path(__file__).parent.parent / "resource" / "test_data"
    test_data_path.mkdir(parents=True, exist_ok=True)
    return str(test_data_path)
